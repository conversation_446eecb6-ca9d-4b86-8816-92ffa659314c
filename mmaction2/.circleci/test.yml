version: 2.1

# the default pipeline parameters, which will be updated according to
# the results of the path-filtering orb
parameters:
  lint_only:
    type: boolean
    default: true

jobs:
  lint:
    docker:
      - image: cimg/python:3.7.4
    steps:
      - checkout
      - run:
          name: Install pre-commit hook
          command: |
            pip install pre-commit
            pre-commit install
      - run:
          name: Linting
          command: pre-commit run --all-files
      - run:
          name: Check docstring coverage
          command: |
            pip install interrogate
            interrogate -v --ignore-init-method --ignore-module --ignore-nested-functions --ignore-regex "__repr__" --fail-under 50 mmaction
  build_cpu:
    parameters:
      # The python version must match available image tags in
      # https://circleci.com/developer/images/image/cimg/python
      python:
        type: string
      torch:
        type: string
      torchvision:
        type: string
    docker:
      - image: cimg/python:<< parameters.python >>
    resource_class: large
    steps:
      - checkout
      - run:
          name: Install Libraries
          command: |
            sudo apt-get update
            sudo apt-get upgrade
            sudo apt-get install -y ffmpeg libsm6 libxext6 git ninja-build libglib2.0-0 libsm6 libxrender-dev libxext6 libturbojpeg pkg-config
            sudo apt-get install -y libavdevice-dev libavfilter-dev libopus-dev libvpx-dev libsrtp2-dev libsndfile1
      - run:
          name: Configure Python & pip
          command: |
            pip install --upgrade pip
            pip install wheel
      - run:
          name: Install PyTorch
          command: |
            python -V
            pip install torch==<< parameters.torch >>+cpu torchvision==<< parameters.torchvision >>+cpu -f https://download.pytorch.org/whl/torch_stable.html
      - run:
          name: Install mmaction dependencies
          command: |
            pip install git+ssh://**************/open-mmlab/mmengine.git@main
            pip install -U openmim
            mim install 'mmcv >= 2.0.0'
            pip install git+https://**************/open-mmlab/mmdetection.git@dev-3.x
            pip install git+https://github.com/open-mmlab/mmclassification.git@dev-1.x
            pip install git+https://github.com/open-mmlab/mmpretrain.git@dev
            pip install git+https://github.com/open-mmlab/mmpose.git@dev-1.x
            pip install -r requirements.txt
      - run:
          name: Install timm
          command: |
            pip install timm
      - run:
          name: Install transformers
          command: |
            pip install transformers
      - when:
          condition:
            equal: [ "0.10.0", << parameters.torchvision >> ]
          steps:
            - run: python -m pip install pytorchvideo
      - run:
          name: Build and install
          command: |
            pip install -e .
      - run:
          name: Run unittests
          command: |
            coverage run --branch --source mmaction -m pytest tests/
            coverage xml
            coverage report -m
  build_cuda:
    parameters:
      torch:
        type: string
      cuda:
        type: enum
        enum: ["11.1"]
      cudnn:
        type: integer
        default: 8
    machine:
      image: ubuntu-2004-cuda-11.4:202110-01
      # docker_layer_caching: true
    resource_class: gpu.nvidia.small
    steps:
      - checkout
      - run:
          name: Build Docker image
          command: |
            docker build .circleci/docker -t mmaction:gpu --build-arg PYTORCH=<< parameters.torch >> --build-arg CUDA=<< parameters.cuda >> --build-arg CUDNN=<< parameters.cudnn >>
            docker run --gpus all -t -d -v /home/<USER>/project:/mmaction -w /mmaction --name mmaction mmaction:gpu
            docker exec mmaction apt-get update
            docker exec mmaction apt-get upgrade -y
            docker exec mmaction apt-get install -y ffmpeg libsm6 libxext6 git ninja-build libglib2.0-0 libsm6 libxrender-dev libxext6 libturbojpeg pkg-config
            docker exec mmaction apt-get install -y libavdevice-dev libavfilter-dev libopus-dev libvpx-dev libsrtp2-dev libsndfile1
      - run:
          name: Install PytorchVideo and timm
          command: |
            docker exec mmaction pip install timm
            docker exec mmaction python -m pip install pytorchvideo
      - run:
          name: Install transformers
          command: |
            docker exec mmaction pip install transformers
      - run:
          name: Install mmaction dependencies
          command: |
            docker exec mmaction pip install git+https://**************/open-mmlab/mmengine.git@main
            docker exec mmaction pip install -U openmim
            docker exec mmaction mim install 'mmcv >= 2.0.0'
            docker exec mmaction pip install git+https://**************/open-mmlab/mmdetection.git@dev-3.x
            docker exec mmaction pip install git+https://**************/open-mmlab/mmpose.git@dev-1.x
            docker exec mmaction pip install git+https://github.com/open-mmlab/mmclassification.git@dev-1.x
            docker exec mmaction pip install git+https://github.com/open-mmlab/mmpretrain.git@dev
            docker exec mmaction pip install -r requirements.txt
      - run:
          name: Build and install
          command: |
            docker exec mmaction pip install -e .
      - run:
          name: Run unittests
          command: |
            docker exec mmaction pytest tests/
workflows:
  pr_stage_lint:
    jobs:
      - lint:
          name: lint
          filters:
            branches:
              ignore:
                - dev-1.x
                - main
  pr_stage_test:
    when:
      not:
        << pipeline.parameters.lint_only >>
    jobs:
      - lint:
          name: lint
          filters:
            branches:
              ignore:
                - dev-1.x
                - main
      - build_cpu:
          name: minimum_version_cpu
          torch: 1.8.1
          torchvision: 0.9.1
          python: 3.7.4
          requires:
            - lint
      - build_cpu:
          name: maximum_version_cpu
          torch: 1.13.0
          torchvision: 0.14.0
          python: 3.9.0
          requires:
            - minimum_version_cpu
      - hold:
          type: approval
          requires:
            - maximum_version_cpu
      - build_cuda:
          name: mainstream_version_gpu
          torch: 1.8.1
          # Use double quotation mark to explicitly specify its type
          # as string instead of number
          cuda: "11.1"
          requires:
            - hold
  merge_stage_test:
    when:
      not:
        << pipeline.parameters.lint_only >>
    jobs:
      - build_cuda:
          name: minimum_version_gpu
          torch: 1.8.1
          # Use double quotation mark to explicitly specify its type
          # as string instead of number
          cuda: "11.1"
          filters:
            branches:
              only:
                - dev-1.x
                - main
