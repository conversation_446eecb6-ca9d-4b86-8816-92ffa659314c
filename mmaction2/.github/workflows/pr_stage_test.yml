name: pr_stage_test

on:
  pull_request:
    paths-ignore:
      - 'README.md'
      - 'README_zh-CN.md'
      - 'docs/**'
      - 'demo/**'
      - '.dev_scripts/**'
      - '.circleci/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_cpu:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: [3.7]
        include:
          - torch: 1.8.1
            torchvision: 0.9.1
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
           python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: pip install pip --upgrade && pip install wheel
      - name: Install soundfile lib
        run: sudo apt-get install -y libsndfile1
      - name: Install librosa and soundfile
        run: pip install librosa soundfile
      - name: Install lmdb
        run: pip install lmdb
      - name: Install TurboJpeg lib
        run: sudo apt-get install -y libturbojpeg
      - name: Install PyTorch
        run: pip install torch==${{matrix.torch}}+cpu torchvision==${{matrix.torchvision}}+cpu -f https://download.pytorch.org/whl/cpu/torch_stable.html
      - name: Install MMEngine
        run: pip install git+https://github.com/open-mmlab/mmengine.git@main
      - name: Install MMCV
        run: |
          pip install -U openmim
          mim install 'mmcv >= 2.0.0'
      - name: Install MMDet
        run: pip install git+https://github.com/open-mmlab/mmdetection.git@dev-3.x
      - name: Install MMCls
        run: pip install git+https://github.com/open-mmlab/mmclassification.git@dev-1.x
      - name: Install MMPretrain
        run: pip install git+https://github.com/open-mmlab/mmpretrain.git@dev
      - name: Install MMPose
        run: pip install git+https://github.com/open-mmlab/mmpose.git@dev-1.x
      - name: Install unittest dependencies
        run: pip install -r requirements.txt
      - name: Install PytorchVideo
        run: pip install pytorchvideo
        # if: ${{matrix.torchvision == '0.10.0'}}
      - name: Install timm
        run: python -m pip install timm
      - name: Build and install
        run: rm -rf .eggs && pip install -e .
      - name: Run unittests and generate coverage report
        run: |
          coverage run --branch --source mmaction -m pytest tests/
          coverage xml
          coverage report -m
      # Only upload coverage report for python3.7 && pytorch1.8.1 cpu
      - name: Upload coverage to Codecov
        if: ${{matrix.torch == '1.8.1' && matrix.python-version == '3.7'}}
        uses: codecov/codecov-action@v1.0.14
        with:
          file: ./coverage.xml
          flags: unittests
          env_vars: OS,PYTHON
          name: codecov-umbrella
          fail_ci_if_error: false

  build_cu102:
    runs-on: ubuntu-22.04
    container:
      image: pytorch/pytorch:1.8.1-cuda10.2-cudnn7-devel
    env:
      MKL_THREADING_LAYER: GNU
    strategy:
      matrix:
        python-version: [3.7]
        include:
          - torch: 1.8.1
            cuda: 10.2
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: pip install pip --upgrade && pip install wheel
      - name: Fetch GPG keys
        run: |
          apt-key adv --fetch-keys https://developer.download.nvidia.com/compute/cuda/repos/ubuntu1804/x86_64/3bf863cc.pub
          apt-key adv --fetch-keys https://developer.download.nvidia.com/compute/machine-learning/repos/ubuntu1804/x86_64/7fa2af80.pub
      - name: Install Python-dev
        run: apt-get update && apt-get install -y python${{matrix.python-version}}-dev
        if: ${{matrix.python-version != 3.9}}
      - name: Install system dependencies
        run: |
          apt-get update && apt-get install -y ffmpeg libsm6 libxext6 git ninja-build libglib2.0-0 libturbojpeg libsndfile1 libsm6 libxrender-dev libxext6
      - name: Install librosa and soundfile
        run: pip install librosa soundfile
      - name: Install lmdb
        run: pip install lmdb
      - name: Install mmaction dependencies
        run: |
          pip install git+https://github.com/open-mmlab/mmengine.git@main
          pip install -U openmim
          mim install 'mmcv >= 2.0.0'
          pip install git+https://github.com/open-mmlab/mmdetection.git@dev-3.x
          pip install git+https://github.com/open-mmlab/mmclassification.git@dev-1.x
          pip install git+https://github.com/open-mmlab/mmpretrain.git@dev
          pip install git+https://github.com/open-mmlab/mmpose.git@dev-1.x
          pip install -r requirements.txt
      - name: Install PytorchVideo
        run: pip install pytorchvideo
        if: ${{matrix.cuda == '10.2'}}
      - name: Build and install
        run: |
          pip install -e . -v
      - name: Run unittests and generate coverage report
        run: |
          coverage run --branch --source mmaction -m pytest tests/ -k 'not timm'
          coverage xml
          coverage report -m

  build_windows:
    runs-on: windows-2022
    strategy:
      matrix:
        os: [windows-2022]
        python-version: [3.7]
        torch: [1.8.1]
        torchvision: [0.9.1]
        platform: [cpu, cu111]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: |
          python -V
          python -m pip install pip --upgrade
          python -m pip install wheel
      - name: Install librosa and soundfile
        run: python -m pip install librosa soundfile
      - name: Install lmdb
        run: pip install lmdb
      - name: Install PyTorch
        run: pip install torch==${{matrix.torch}}+${{matrix.platform}} torchvision==${{matrix.torchvision}}+${{matrix.platform}} -f https://download.pytorch.org/whl/${{matrix.platform}}/torch_stable.html
      - name: Install timm
        run: python -m pip install timm
      - name: Install mmaction dependencies
        run: |
          pip install git+https://github.com/open-mmlab/mmengine.git@main
          pip install -U openmim
          mim install 'mmcv >= 2.0.0'
          pip install git+https://github.com/open-mmlab/mmdetection.git@dev-3.x
          pip install git+https://github.com/open-mmlab/mmclassification.git@dev-1.x
          pip install git+https://github.com/open-mmlab/mmpretrain.git@dev
          pip install git+https://github.com/open-mmlab/mmpose.git@dev-1.x
          pip install -r requirements.txt
      - name: Install PytorchVideo
        run: python -m pip install pytorchvideo
      - name: Build and install
        run: |
          pip install -e . -v
      - name: Run unittests and generate coverage report
        run: |
          pytest tests/
