name: "🐞 Bug Report"
description: "Create a report to help us reproduce and fix the bug"
labels: Bug
title: "[Bug] "

body:
  - type: markdown
    attributes:
      value: |
        If you have already identified the reason, we strongly appreciate you creating a new PR to fix it [here](https://github.com/open-mmlab/mmaction2/pulls)!
        If this issue is about installing MMCV, please file an issue at [MMCV](https://github.com/open-mmlab/mmcv/issues/new/choose).
        If you need our help, please fill in as much of the following form as you're able.

        **The less clear the description, the longer it will take to solve it.**

  - type: dropdown
    id: version
    attributes:
      label: Branch
      description: Which branch/version are you using?
      options:
        - main branch (1.x version, such as `v1.0.0`, or `dev-1.x` branch)
        - 0.x branch (0.x version, such as `v0.24.1`)
    validations:
      required: true

  - type: checkboxes
    attributes:
      label: Prerequisite
      description: Please check the following items before creating a new issue.
      options:
      - label: I have searched [Issues](https://github.com/open-mmlab/mmaction2/issues) and [Discussions](https://github.com/open-mmlab/mmaction2/discussions) but cannot get the expected help.
        required: true
      - label: I have read the [documentation](https://mmaction2.readthedocs.io/en/latest/) but cannot get the expected help.
        required: true
      - label: The bug has not been fixed in the [latest version](https://github.com/open-mmlab/mmaction2).
        required: true

  - type: textarea
    attributes:
      label: Environment
      description: |
        Please run `python mmaction2/utils/collect_env.py` to collect necessary environment information and copy-paste it here.
        You may add additional information that may be helpful for locating the problem, such as
          - How you installed PyTorch \[e.g., pip, conda, source\]
          - Other environment variables that may be related (such as `$PATH`, `$LD_LIBRARY_PATH`, `$PYTHONPATH`, etc.)
    validations:
      required: true

  - type: textarea
    id: description
    validations:
      required: true
    attributes:
      label: Describe the bug
      description: |
        Please provide a clear and concise description of what the bug is.
        Preferably a simple and minimal code snippet is provided below, so that we can reproduce the error by running the code.
      placeholder: |
        A clear and concise description of what the bug is.

  - type: textarea
    attributes:
      label: Reproduces the problem - code sample
      description: |
        Please provide a code sample that reproduces the problem you ran into. It can be a Colab link or just a code snippet.
        Did you make any modifications on the code or config? Are you clear about what you have modified?
      placeholder: |
        ```python
        # Sample code to reproduce the problem
        ```

  - type: textarea
    attributes:
      label: Reproduces the problem - command or script
      description: |
        What command or script did you run?
      placeholder: |
        ```shell
        The command or script you run.
        ```

  - type: textarea
    attributes:
      label: Reproduces the problem - error message
      description: |
        Please provide the error message or logs you got, with the full traceback.
      placeholder: |
        ```
        The error message or logs you got, with the full traceback.
        ```

  - type: textarea
    attributes:
      label: Additional information
      description: Tell us anything else you think we should know.
      placeholder: |
        1. What's your expected result?
        2. What dataset did you use?
        3. What do you think might be the reason?
