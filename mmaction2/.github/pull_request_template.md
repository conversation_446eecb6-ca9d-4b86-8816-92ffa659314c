Thanks for your contribution and we appreciate it a lot. The following instructions would make your pull request more healthy and more easily got feedback.
If you do not understand some items, don't worry, just make the pull request and seek help from maintainers.

## Motivation

Please describe the motivation of this PR and the goal you want to achieve through this PR.

## Modification

Please briefly describe what modification is made in this PR.

## BC-breaking (Optional)

Does the modification introduces changes that break the back-compatibility of this repo?
If so, please describe how it breaks the compatibility and how users should modify their codes to keep compatibility with this PR.

## Use cases (Optional)

If this PR introduces a new feature, it is better to list some use cases here, and update the documentation.

## Checklist

1. Pre-commit or other linting tools should be used to fix the potential lint issues.
2. The modification should be covered by complete unit tests. If not, please add more unit tests to ensure the correctness.
3. If the modification has potential influence on downstream projects, this PR should be tested with downstream projects, like MMDet or MMCls.
4. The documentation should be modified accordingly, like docstring or example tutorials.
