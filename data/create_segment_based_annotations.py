#!/usr/bin/env python3
"""
基于视频片段创建TAD annotation
每个片段作为一个完整的"视频"，其中包含一个动作
"""

import os
import json
import cv2
import pandas as pd
from pathlib import Path

def create_segment_based_annotations(video_dir, output_dir):
    """
    为每个视频片段创建独立的annotation
    每个片段被视为一个完整的视频，包含一个动作
    """
    video_dir = Path(video_dir)
    video_files = list(video_dir.glob('*.mp4'))
    
    annotation_db = {}
    
    for video_file in video_files:
        print(f"处理视频片段: {video_file.name}")
        
        # 获取视频信息
        cap = cv2.VideoCapture(str(video_file))
        if not cap.isOpened():
            print(f"无法打开视频: {video_file.name}")
            continue
            
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        
        # 使用文件名（不含扩展名）作为视频ID
        video_id = video_file.stem
        
        # 创建annotation：整个片段就是一个动作
        annotation_db[video_id] = {
            "duration_second": duration,
            "duration_frame": frame_count,
            "annotations": [
                {
                    "segment": [0.0, duration],  # 整个片段都是动作
                    "label": "action"  # 统一的动作标签
                }
            ],
            "feature_frame": frame_count,
            "fps": fps,
            "rfps": fps
        }
    
    return annotation_db

def create_features_for_segments(video_dir, feature_dir):
    """
    为每个视频片段创建特征文件
    """
    video_dir = Path(video_dir)
    feature_dir = Path(feature_dir)
    feature_dir.mkdir(exist_ok=True)
    
    video_files = list(video_dir.glob('*.mp4'))
    
    for video_file in video_files:
        print(f"为片段提取特征: {video_file.name}")
        
        # 读取视频
        cap = cv2.VideoCapture(str(video_file))
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        cap.release()
        
        if not frames:
            continue
            
        # 创建时序特征
        # 对于短片段，我们创建固定数量的时间步（比如16步）
        num_timesteps = 16
        feature_dim = 400
        
        features = []
        frames_per_timestep = max(1, len(frames) // num_timesteps)
        
        for t in range(num_timesteps):
            start_frame = t * frames_per_timestep
            end_frame = min((t + 1) * frames_per_timestep, len(frames))
            
            if start_frame < len(frames):
                # 提取该时间步的特征
                timestep_frames = frames[start_frame:end_frame]
                feature = extract_simple_features(timestep_frames)
                features.append(feature)
            else:
                # 如果没有足够的帧，使用零特征
                features.append([0.0] * feature_dim)
        
        # 保存特征为CSV
        video_id = video_file.stem
        feature_file = feature_dir / f"{video_id}.csv"
        
        # 保存为CSV格式（无header，无index）
        import pandas as pd
        df = pd.DataFrame(features)
        df.to_csv(feature_file, header=False, index=False)

def extract_simple_features(frames):
    """
    提取简单的视觉特征
    """
    if not frames:
        return [0.0] * 400
    
    import numpy as np
    
    # 计算颜色直方图特征
    hist_features = []
    for frame in frames:
        # RGB直方图
        for channel in range(3):
            hist = cv2.calcHist([frame], [channel], None, [32], [0, 256])
            hist_features.extend(hist.flatten())
    
    # 计算运动特征（帧差）
    if len(frames) > 1:
        motion_features = []
        for i in range(len(frames) - 1):
            gray1 = cv2.cvtColor(frames[i], cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(frames[i + 1], cv2.COLOR_BGR2GRAY)
            diff = cv2.absdiff(gray1, gray2)
            motion_features.append(np.mean(diff))
        
        # 填充到固定长度
        while len(motion_features) < 50:
            motion_features.append(0.0)
        motion_features = motion_features[:50]
    else:
        motion_features = [0.0] * 50
    
    # 合并特征
    all_features = hist_features + motion_features
    
    # 填充或截断到400维
    if len(all_features) < 400:
        all_features.extend([0.0] * (400 - len(all_features)))
    else:
        all_features = all_features[:400]
    
    return all_features

def split_annotations(annotation_db, train_ratio=0.8):
    """
    分割数据集
    """
    import random
    random.seed(42)
    
    video_ids = list(annotation_db.keys())
    random.shuffle(video_ids)
    
    split_point = int(len(video_ids) * train_ratio)
    train_ids = video_ids[:split_point]
    val_ids = video_ids[split_point:]
    
    train_data = {vid: annotation_db[vid] for vid in train_ids}
    val_data = {vid: annotation_db[vid] for vid in val_ids}
    
    return train_data, val_data

def main():
    # 路径设置
    video_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/segmented_videos_ok"
    output_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/SegmentTAD"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(f"{output_dir}/features", exist_ok=True)
    
    print("=== 基于视频片段创建TAD数据集 ===")
    
    # 1. 创建annotations
    print("1. 创建annotations...")
    annotation_db = create_segment_based_annotations(video_dir, output_dir)
    print(f"创建了 {len(annotation_db)} 个视频的annotations")
    
    # 2. 提取特征
    print("2. 提取特征...")
    create_features_for_segments(video_dir, f"{output_dir}/features")
    
    # 3. 分割数据集
    print("3. 分割数据集...")
    train_data, val_data = split_annotations(annotation_db, train_ratio=0.8)
    
    # 4. 保存文件
    train_file = f"{output_dir}/segment_tad_train.json"
    val_file = f"{output_dir}/segment_tad_val.json"
    
    with open(train_file, 'w') as f:
        json.dump(train_data, f, indent=2)
    
    with open(val_file, 'w') as f:
        json.dump(val_data, f, indent=2)
    
    print(f"\n=== 完成 ===")
    print(f"训练集: {len(train_data)} 个片段 -> {train_file}")
    print(f"验证集: {len(val_data)} 个片段 -> {val_file}")
    print(f"特征文件保存在: {output_dir}/features/")
    
    # 统计信息
    total_duration = sum(video['duration_second'] for video in annotation_db.values())
    print(f"总时长: {total_duration:.2f} 秒")
    print(f"平均片段长度: {total_duration/len(annotation_db):.2f} 秒")

if __name__ == "__main__":
    main()
