#!/usr/bin/env python3
"""
测试多类别TAD数据加载和格式
"""

import sys
import os
import json
import pandas as pd
from collections import Counter

sys.path.insert(0, '/home/<USER>/johnny_ws/mmaction2_ws/mmaction2')

from mmaction.datasets import ActivityNetDataset
from mmaction.utils import register_all_modules

def test_multiclass_annotations():
    """测试多类别annotation文件格式"""
    print("=== 测试多类别Annotation文件格式 ===")
    
    # 测试训练集
    train_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/multiclass_tad_train.json"
    with open(train_file, 'r') as f:
        train_data = json.load(f)
    
    # 测试验证集
    val_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/multiclass_tad_val.json"
    with open(val_file, 'r') as f:
        val_data = json.load(f)
    
    # 测试类别映射
    class_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/class_mapping.json"
    with open(class_file, 'r') as f:
        class_mapping = json.load(f)
    
    print(f"训练集视频数量: {len(train_data)}")
    print(f"验证集视频数量: {len(val_data)}")
    print(f"类别映射: {class_mapping}")
    
    # 统计各类别数量
    train_labels = []
    val_labels = []
    
    for video_data in train_data.values():
        for ann in video_data['annotations']:
            train_labels.append(ann['label'])
    
    for video_data in val_data.values():
        for ann in video_data['annotations']:
            val_labels.append(ann['label'])
    
    train_counter = Counter(train_labels)
    val_counter = Counter(val_labels)
    
    print(f"\n训练集类别分布:")
    for label, count in train_counter.items():
        print(f"  {label}: {count} 个")
    
    print(f"\n验证集类别分布:")
    for label, count in val_counter.items():
        print(f"  {label}: {count} 个")
    
    # 检查数据平衡性
    print(f"\n数据平衡性分析:")
    total_train = sum(train_counter.values())
    for label, count in train_counter.items():
        percentage = (count / total_train) * 100
        print(f"  {label}: {percentage:.1f}%")
    
    return True

def test_multiclass_dataset_loading():
    """测试MMAction2多类别数据集加载"""
    print("\n=== 测试MMAction2多类别数据集加载 ===")
    
    try:
        # 注册所有模块
        register_all_modules()
        
        # 创建数据集
        ann_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/multiclass_tad_train.json"
        data_prefix = dict(video="/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/features/")
        
        pipeline = [
            dict(type='LoadLocalizationFeature'),
            dict(type='GenerateLocalizationLabels'),
            dict(
                type='PackLocalizationInputs',
                keys=('gt_bbox', ),
                meta_keys=('video_name', ))
        ]
        
        dataset = ActivityNetDataset(
            ann_file=ann_file,
            pipeline=pipeline,
            data_prefix=data_prefix,
            test_mode=False
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        # 测试加载几个不同类别的样本
        print(f"\n测试加载不同类别的样本:")
        
        # 读取annotation文件来找到不同类别的样本
        with open(ann_file, 'r') as f:
            ann_data = json.load(f)
        
        # 按类别分组样本
        samples_by_class = {}
        for idx, (video_name, video_data) in enumerate(ann_data.items()):
            label = video_data['annotations'][0]['label']
            if label not in samples_by_class:
                samples_by_class[label] = []
            samples_by_class[label].append(idx)
        
        # 测试每个类别的第一个样本
        for label, indices in samples_by_class.items():
            if indices:
                idx = indices[0]
                sample = dataset[idx]
                print(f"  类别 '{label}' (索引 {idx}):")
                print(f"    输入特征形状: {sample['inputs'].shape}")
                if 'gt_bbox' in sample:
                    print(f"    GT bbox形状: {sample['gt_bbox'].shape}")
                print(f"    视频名: {sample['data_samples'].video_name}")
        
        print("多类别数据集加载成功！")
        return True
        
    except Exception as e:
        print(f"多类别数据集加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiclass_config():
    """测试多类别配置文件"""
    print("\n=== 测试多类别配置文件 ===")
    
    config_file = "/home/<USER>/johnny_ws/mmaction2_ws/mmaction2/configs/localization/bmn/bmn_2xb4-16x400-20e_multiclass-tad-feature.py"
    
    try:
        from mmengine.config import Config
        cfg = Config.fromfile(config_file)
        
        print("多类别配置文件加载成功！")
        print(f"模型类型: {cfg.model.type}")
        print(f"时间维度: {cfg.model.temporal_dim}")
        print(f"特征维度: {cfg.model.feat_dim}")
        print(f"训练epochs: {cfg.max_epochs}")
        print(f"batch size: {cfg.train_dataloader.batch_size}")
        print(f"类别数量: {cfg.num_classes}")
        print(f"类别名称: {cfg.class_names}")
        
        return True
        
    except Exception as e:
        print(f"多类别配置文件加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_inference_script():
    """创建推理脚本模板，支持按类别输出到不同文件夹"""
    print("\n=== 创建推理脚本模板 ===")
    
    inference_script = '''#!/usr/bin/env python3
"""
多类别TAD推理脚本
根据预测结果将视频分类到不同的输出文件夹
"""

import os
import json
import shutil
from pathlib import Path

def create_output_directories(base_output_dir):
    """创建输出目录结构"""
    class_dirs = {
        'ok': os.path.join(base_output_dir, 'ok'),
        'nok_electric_defect': os.path.join(base_output_dir, 'nok_electric_defect'),
        'nok_appearance_defect': os.path.join(base_output_dir, 'nok_appearance_defect'),
        'unknown': os.path.join(base_output_dir, 'unknown')
    }
    
    for class_name, class_dir in class_dirs.items():
        os.makedirs(class_dir, exist_ok=True)
        print(f"创建输出目录: {class_dir}")
    
    return class_dirs

def process_inference_results(results_file, video_dir, output_dirs):
    """
    处理推理结果，将视频文件复制到对应的类别文件夹
    
    Args:
        results_file: 推理结果JSON文件路径
        video_dir: 原始视频文件目录
        output_dirs: 输出目录字典
    """
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    # 类别映射
    class_mapping = {
        0: 'nok_appearance_defect',
        1: 'nok_electric_defect', 
        2: 'ok'
    }
    
    for video_name, predictions in results.items():
        # 获取预测的类别（这里需要根据实际的结果格式调整）
        predicted_class_id = get_predicted_class(predictions)
        predicted_class = class_mapping.get(predicted_class_id, 'unknown')
        
        # 找到对应的视频文件
        video_file = find_video_file(video_name, video_dir)
        
        if video_file and os.path.exists(video_file):
            # 复制到对应的输出目录
            output_dir = output_dirs[predicted_class]
            output_file = os.path.join(output_dir, os.path.basename(video_file))
            
            shutil.copy2(video_file, output_file)
            print(f"视频 {video_name} 预测为 {predicted_class}, 已复制到 {output_file}")
        else:
            print(f"警告: 找不到视频文件 {video_name}")

def get_predicted_class(predictions):
    """从预测结果中提取类别ID（需要根据实际格式调整）"""
    # 这里需要根据BMN模型的实际输出格式来实现
    # 示例实现：
    if 'predictions' in predictions:
        return predictions['predictions'][0]['label_id']
    return 0  # 默认返回第一个类别

def find_video_file(video_name, video_dir):
    """在视频目录中查找对应的视频文件"""
    video_dir = Path(video_dir)
    
    # 尝试不同的扩展名
    for ext in ['.mp4', '.avi', '.mov']:
        video_file = video_dir / f"{video_name}{ext}"
        if video_file.exists():
            return str(video_file)
    
    return None

def main():
    # 配置路径
    results_file = "work_dirs/bmn_multiclass_tad/results.json"  # 推理结果文件
    video_dir = "data/segmented_videos_ok"  # 原始视频目录
    base_output_dir = "output/classified_videos"  # 输出基础目录
    
    # 创建输出目录
    output_dirs = create_output_directories(base_output_dir)
    
    # 处理推理结果
    if os.path.exists(results_file):
        process_inference_results(results_file, video_dir, output_dirs)
        print("视频分类完成！")
    else:
        print(f"推理结果文件不存在: {results_file}")
        print("请先运行推理命令:")
        print("python tools/test.py configs/localization/bmn/bmn_2xb4-16x400-20e_multiclass-tad-feature.py work_dirs/bmn_multiclass_tad/latest.pth")

if __name__ == "__main__":
    main()
'''
    
    # 保存推理脚本
    script_path = "/home/<USER>/johnny_ws/mmaction2_ws/classify_videos_by_prediction.py"
    with open(script_path, 'w') as f:
        f.write(inference_script)
    
    print(f"推理脚本已保存到: {script_path}")
    print("使用方法:")
    print("1. 先训练模型")
    print("2. 运行推理获得结果")
    print("3. 运行此脚本将视频按预测结果分类到不同文件夹")

def main():
    """主函数"""
    print("开始验证多类别TAD数据格式...")
    
    success = True
    
    # 测试annotation格式
    success &= test_multiclass_annotations()
    
    # 测试数据集加载
    success &= test_multiclass_dataset_loading()
    
    # 测试配置文件
    success &= test_multiclass_config()
    
    # 创建推理脚本
    create_inference_script()
    
    print(f"\n=== 验证结果 ===")
    if success:
        print("✅ 所有测试通过！多类别TAD数据格式正确，可以开始训练。")
        print("\n下一步可以运行:")
        print("cd /home/<USER>/johnny_ws/mmaction2_ws/mmaction2")
        print("python tools/train.py configs/localization/bmn/bmn_2xb4-16x400-20e_multiclass-tad-feature.py")
        print("\n训练完成后，可以使用生成的推理脚本将视频按预测结果分类到不同文件夹。")
    else:
        print("❌ 部分测试失败，请检查数据格式。")

if __name__ == "__main__":
    main()
