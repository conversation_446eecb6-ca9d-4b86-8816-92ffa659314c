#!/usr/bin/env python3
"""
创建多类别TAD annotation
根据不同目录分配不同的标签：
- segmented_videos_ok -> "ok"
- segmented_videos_nok_electric -> "nok_electric_defect"
- segmented_videos_nok_appearance -> "nok_appearance_defect"
"""

import os
import json
import cv2
import pandas as pd
from pathlib import Path
import random

def get_label_from_path(video_path):
    """根据视频路径确定标签"""
    path_str = str(video_path)

    if "segmented_videos_ok" in path_str:
        return "ok"
    elif "segmented_videos_nok_electric" in path_str:
        return "nok_electric_defect"
    elif "segmented_videos_nok_appearance" in path_str:
        return "nok_appearance_defect"
    else:
        return "unknown"

def process_video_directory(video_dir, label):
    """处理单个视频目录"""
    video_dir = Path(video_dir)
    if not video_dir.exists():
        print(f"目录不存在: {video_dir}")
        return []
    
    video_files = list(video_dir.glob('*.mp4'))
    video_info_list = []
    
    print(f"处理目录: {video_dir} (标签: {label})")
    print(f"找到 {len(video_files)} 个视频文件")
    
    for video_file in video_files:
        print(f"  处理: {video_file.name}")
        
        # 获取视频信息
        cap = cv2.VideoCapture(str(video_file))
        if not cap.isOpened():
            print(f"    无法打开视频: {video_file.name}")
            continue
            
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        
        # 使用文件名（不含扩展名）作为视频ID
        video_id = video_file.stem
        
        video_info = {
            'video_id': video_id,
            'video_path': str(video_file),
            'duration_second': duration,
            'duration_frame': frame_count,
            'fps': fps,
            'label': label
        }
        
        video_info_list.append(video_info)
    
    return video_info_list

def create_multiclass_annotations(video_dirs_and_labels):
    """创建多类别annotations"""
    all_video_info = []
    
    # 处理每个目录
    for video_dir, label in video_dirs_and_labels:
        video_info_list = process_video_directory(video_dir, label)
        all_video_info.extend(video_info_list)
    
    # 创建annotation数据库
    annotation_db = {}
    
    for info in all_video_info:
        video_id = info['video_id']
        
        annotation_db[video_id] = {
            "duration_second": info['duration_second'],
            "duration_frame": info['duration_frame'],
            "annotations": [
                {
                    "segment": [0.0, info['duration_second']],  # 整个片段都是动作
                    "label": info['label']
                }
            ],
            "feature_frame": info['duration_frame'],
            "fps": info['fps'],
            "rfps": info['fps']
        }
    
    return annotation_db, all_video_info

def extract_features_for_multiclass(all_video_info, feature_dir):
    """为多类别数据提取特征"""
    feature_dir = Path(feature_dir)
    feature_dir.mkdir(exist_ok=True)
    
    print(f"\n开始提取特征到: {feature_dir}")
    
    for info in all_video_info:
        video_path = info['video_path']
        video_id = info['video_id']
        
        print(f"提取特征: {video_id} (标签: {info['label']})")
        
        # 读取视频
        cap = cv2.VideoCapture(video_path)
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        cap.release()
        
        if not frames:
            print(f"  警告: 无法读取帧 {video_id}")
            continue
            
        # 创建时序特征
        num_timesteps = 16
        feature_dim = 400
        
        features = []
        frames_per_timestep = max(1, len(frames) // num_timesteps)
        
        for t in range(num_timesteps):
            start_frame = t * frames_per_timestep
            end_frame = min((t + 1) * frames_per_timestep, len(frames))
            
            if start_frame < len(frames):
                timestep_frames = frames[start_frame:end_frame]
                feature = extract_simple_features(timestep_frames)
                features.append(feature)
            else:
                features.append([0.0] * feature_dim)
        
        # 保存特征为CSV
        feature_file = feature_dir / f"{video_id}.csv"
        df = pd.DataFrame(features)
        df.to_csv(feature_file, header=False, index=False)

def extract_simple_features(frames):
    """提取简单的视觉特征"""
    if not frames:
        return [0.0] * 400
    
    import numpy as np
    
    # 计算颜色直方图特征
    hist_features = []
    for frame in frames:
        for channel in range(3):
            hist = cv2.calcHist([frame], [channel], None, [32], [0, 256])
            hist_features.extend(hist.flatten())
    
    # 计算运动特征（帧差）
    if len(frames) > 1:
        motion_features = []
        for i in range(len(frames) - 1):
            gray1 = cv2.cvtColor(frames[i], cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(frames[i + 1], cv2.COLOR_BGR2GRAY)
            diff = cv2.absdiff(gray1, gray2)
            motion_features.append(np.mean(diff))
        
        while len(motion_features) < 50:
            motion_features.append(0.0)
        motion_features = motion_features[:50]
    else:
        motion_features = [0.0] * 50
    
    # 合并特征
    all_features = hist_features + motion_features
    
    # 填充或截断到400维
    if len(all_features) < 400:
        all_features.extend([0.0] * (400 - len(all_features)))
    else:
        all_features = all_features[:400]
    
    return all_features

def split_multiclass_dataset(annotation_db, train_ratio=0.8):
    """分割多类别数据集，确保每个类别都有训练和验证样本"""
    # 按标签分组
    label_groups = {}
    for video_id, data in annotation_db.items():
        label = data['annotations'][0]['label']
        if label not in label_groups:
            label_groups[label] = []
        label_groups[label].append(video_id)
    
    train_ids = []
    val_ids = []
    
    # 为每个标签分别分割
    random.seed(42)
    for label, video_ids in label_groups.items():
        random.shuffle(video_ids)
        split_point = max(1, int(len(video_ids) * train_ratio))  # 确保至少有1个训练样本
        
        train_ids.extend(video_ids[:split_point])
        val_ids.extend(video_ids[split_point:])
        
        print(f"标签 '{label}': {len(video_ids)} 个样本 -> 训练集: {split_point}, 验证集: {len(video_ids) - split_point}")
    
    train_data = {vid: annotation_db[vid] for vid in train_ids}
    val_data = {vid: annotation_db[vid] for vid in val_ids}
    
    return train_data, val_data

def create_class_mapping(annotation_db):
    """创建类别映射文件"""
    labels = set()
    for data in annotation_db.values():
        for ann in data['annotations']:
            labels.add(ann['label'])
    
    class_mapping = {label: idx for idx, label in enumerate(sorted(labels))}
    return class_mapping

def main():
    # 定义视频目录和对应标签
    base_path = "/home/<USER>/johnny_ws/mmaction2_ws/data"
    video_dirs_and_labels = [
        (f"{base_path}/segmented_videos_ok", "ok"),
        (f"{base_path}/segmented_videos_nok_electric", "nok_electric_defect"),
        (f"{base_path}/segmented_videos_nok_appearance", "nok_appearance_defect")
    ]
    
    output_dir = f"{base_path}/MultiClassTAD"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(f"{output_dir}/features", exist_ok=True)
    
    print("=== 创建多类别TAD数据集 ===")
    
    # 1. 创建annotations
    print("1. 创建多类别annotations...")
    annotation_db, all_video_info = create_multiclass_annotations(video_dirs_and_labels)
    print(f"总共创建了 {len(annotation_db)} 个视频的annotations")
    
    # 2. 提取特征
    print("2. 提取特征...")
    extract_features_for_multiclass(all_video_info, f"{output_dir}/features")
    
    # 3. 创建类别映射
    class_mapping = create_class_mapping(annotation_db)
    print(f"3. 类别映射: {class_mapping}")
    
    # 4. 分割数据集
    print("4. 分割数据集...")
    train_data, val_data = split_multiclass_dataset(annotation_db, train_ratio=0.8)
    
    # 5. 保存文件
    train_file = f"{output_dir}/multiclass_tad_train.json"
    val_file = f"{output_dir}/multiclass_tad_val.json"
    class_file = f"{output_dir}/class_mapping.json"
    
    with open(train_file, 'w') as f:
        json.dump(train_data, f, indent=2)
    
    with open(val_file, 'w') as f:
        json.dump(val_data, f, indent=2)
    
    with open(class_file, 'w') as f:
        json.dump(class_mapping, f, indent=2)
    
    print(f"\n=== 完成 ===")
    print(f"训练集: {len(train_data)} 个片段 -> {train_file}")
    print(f"验证集: {len(val_data)} 个片段 -> {val_file}")
    print(f"类别映射: {class_file}")
    print(f"特征文件保存在: {output_dir}/features/")
    
    # 统计信息
    print(f"\n=== 统计信息 ===")
    for label, idx in class_mapping.items():
        train_count = sum(1 for data in train_data.values() if data['annotations'][0]['label'] == label)
        val_count = sum(1 for data in val_data.values() if data['annotations'][0]['label'] == label)
        print(f"类别 '{label}' (ID: {idx}): 训练集 {train_count} 个, 验证集 {val_count} 个")

if __name__ == "__main__":
    main()
