#!/usr/bin/env python3
"""
测试数据加载和格式是否正确
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/johnny_ws/mmaction2_ws/mmaction2')

import json
import pandas as pd
from mmaction.datasets import ActivityNetDataset
from mmaction.utils import register_all_modules

def test_annotation_format():
    """测试annotation文件格式"""
    print("=== 测试Annotation文件格式 ===")
    
    # 测试训练集
    train_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/SegmentTAD/segment_tad_train.json"
    with open(train_file, 'r') as f:
        train_data = json.load(f)
    
    print(f"训练集视频数量: {len(train_data)}")
    
    # 检查第一个视频的格式
    first_video = list(train_data.keys())[0]
    first_data = train_data[first_video]
    
    print(f"第一个视频: {first_video}")
    print(f"  时长: {first_data['duration_second']:.2f}秒")
    print(f"  帧数: {first_data['duration_frame']}")
    print(f"  FPS: {first_data['fps']}")
    print(f"  动作数量: {len(first_data['annotations'])}")
    
    for i, ann in enumerate(first_data['annotations']):
        print(f"  动作{i+1}: {ann['segment'][0]:.2f}s - {ann['segment'][1]:.2f}s, 标签: {ann['label']}")
    
    # 测试验证集
    val_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/SegmentTAD/segment_tad_val.json"
    with open(val_file, 'r') as f:
        val_data = json.load(f)
    
    print(f"验证集视频数量: {len(val_data)}")
    
    return True

def test_feature_files():
    """测试特征文件"""
    print("\n=== 测试特征文件 ===")
    
    feature_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/SegmentTAD/features"
    feature_files = [f for f in os.listdir(feature_dir) if f.endswith('.csv')]
    
    print(f"特征文件数量: {len(feature_files)}")
    
    # 检查第一个特征文件
    first_feature = feature_files[0]
    feature_path = os.path.join(feature_dir, first_feature)
    
    df = pd.read_csv(feature_path, header=None)
    print(f"第一个特征文件: {first_feature}")
    print(f"  形状: {df.shape} (时间步 x 特征维度)")
    print(f"  数据类型: {df.dtypes[0]}")
    print(f"  数值范围: {df.min().min():.3f} ~ {df.max().max():.3f}")
    
    # 检查是否有NaN值
    nan_count = df.isnull().sum().sum()
    print(f"  NaN值数量: {nan_count}")
    
    return True

def test_dataset_loading():
    """测试MMAction2数据集加载"""
    print("\n=== 测试MMAction2数据集加载 ===")
    
    try:
        # 注册所有模块
        register_all_modules()
        
        # 创建数据集
        ann_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/SegmentTAD/segment_tad_train.json"
        data_prefix = dict(video="/home/<USER>/johnny_ws/mmaction2_ws/data/SegmentTAD/features/")
        
        pipeline = [
            dict(type='LoadLocalizationFeature'),
            dict(type='GenerateLocalizationLabels'),
            dict(
                type='PackLocalizationInputs',
                keys=('gt_bbox', ),
                meta_keys=('video_name', ))
        ]
        
        dataset = ActivityNetDataset(
            ann_file=ann_file,
            pipeline=pipeline,
            data_prefix=data_prefix,
            test_mode=False
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        # 测试加载第一个样本
        sample = dataset[0]
        print(f"第一个样本的键: {list(sample.keys())}")
        
        if 'inputs' in sample:
            print(f"输入特征形状: {sample['inputs'].shape}")
        
        if 'gt_bbox' in sample:
            print(f"GT bbox形状: {sample['gt_bbox'].shape}")
        
        print("数据集加载成功！")
        return True
        
    except Exception as e:
        print(f"数据集加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file():
    """测试配置文件"""
    print("\n=== 测试配置文件 ===")
    
    config_file = "/home/<USER>/johnny_ws/mmaction2_ws/mmaction2/configs/localization/bmn/bmn_2xb4-16x400-20e_segment-tad-feature.py"
    
    try:
        from mmengine.config import Config
        cfg = Config.fromfile(config_file)
        
        print("配置文件加载成功！")
        print(f"模型类型: {cfg.model.type}")
        print(f"时间维度: {cfg.model.temporal_dim}")
        print(f"特征维度: {cfg.model.feat_dim}")
        print(f"训练epochs: {cfg.max_epochs}")
        print(f"batch size: {cfg.train_dataloader.batch_size}")
        
        return True
        
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始验证TAD数据格式...")
    
    success = True
    
    # 测试annotation格式
    success &= test_annotation_format()
    
    # 测试特征文件
    success &= test_feature_files()
    
    # 测试数据集加载
    success &= test_dataset_loading()
    
    # 测试配置文件
    success &= test_config_file()
    
    print(f"\n=== 验证结果 ===")
    if success:
        print("✅ 所有测试通过！数据格式正确，可以开始训练。")
        print("\n下一步可以运行:")
        print("cd /home/<USER>/johnny_ws/mmaction2_ws/mmaction2")
        print("python tools/train.py ../configs/localization/bmn/bmn_2xb4-16x400-20e_segment-tad-feature.py")
    else:
        print("❌ 部分测试失败，请检查数据格式。")

if __name__ == "__main__":
    main()
