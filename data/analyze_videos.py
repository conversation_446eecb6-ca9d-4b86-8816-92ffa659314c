#!/usr/bin/env python3
"""
分析segmented_videos_ok目录下的视频文件，提取时间信息并准备TAD数据格式
"""

import os
import re
import json
import cv2
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd

def parse_filename(filename):
    """
    解析视频文件名，提取时间信息
    例如: 20250727T075411Z_20250727T075911Z_decrypted-00.00.03.481-00.00.05.934-seg01_roi.mp4
    """
    # 移除扩展名
    name_without_ext = filename.replace('.mp4', '')
    
    # 提取各部分信息
    pattern = r'(\d{8}T\d{6}Z)_(\d{8}T\d{6}Z)_decrypted-(\d{2}\.\d{2}\.\d{2}\.\d{3})-(\d{2}\.\d{2}\.\d{2}\.\d{3})-seg(\d+)_roi'
    match = re.match(pattern, name_without_ext)
    
    if match:
        start_timestamp = match.group(1)
        end_timestamp = match.group(2)
        segment_start = match.group(3)
        segment_end = match.group(4)
        segment_id = match.group(5)
        
        # 转换时间格式
        def time_str_to_seconds(time_str):
            """将 00.00.03.481 格式转换为秒数"""
            parts = time_str.split('.')
            minutes = int(parts[0])
            seconds = int(parts[1])
            subsec1 = int(parts[2])
            subsec2 = int(parts[3])
            return minutes * 60 + seconds + subsec1 + subsec2 / 1000.0
        
        start_sec = time_str_to_seconds(segment_start)
        end_sec = time_str_to_seconds(segment_end)
        duration = end_sec - start_sec
        
        return {
            'filename': filename,
            'original_start_timestamp': start_timestamp,
            'original_end_timestamp': end_timestamp,
            'segment_start_time': start_sec,
            'segment_end_time': end_sec,
            'segment_duration': duration,
            'segment_id': int(segment_id),
            'video_name': f"{start_timestamp}_{end_timestamp}_decrypted"
        }
    else:
        print(f"无法解析文件名: {filename}")
        return None

def get_video_info(video_path):
    """获取视频的基本信息"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return None
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = frame_count / fps if fps > 0 else 0
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    cap.release()
    
    return {
        'fps': fps,
        'frame_count': frame_count,
        'duration_seconds': duration,
        'width': width,
        'height': height
    }

def analyze_video_directory(video_dir):
    """分析视频目录，返回所有视频的信息"""
    video_dir = Path(video_dir)
    video_files = list(video_dir.glob('*.mp4'))
    
    video_info_list = []
    
    for video_file in video_files:
        print(f"分析视频: {video_file.name}")
        
        # 解析文件名
        parsed_info = parse_filename(video_file.name)
        if parsed_info is None:
            continue
            
        # 获取视频信息
        video_info = get_video_info(str(video_file))
        if video_info is None:
            print(f"无法读取视频信息: {video_file.name}")
            continue
            
        # 合并信息
        combined_info = {**parsed_info, **video_info}
        video_info_list.append(combined_info)
    
    return video_info_list

def group_videos_by_original(video_info_list):
    """按原始视频分组"""
    grouped = {}
    for info in video_info_list:
        video_name = info['video_name']
        if video_name not in grouped:
            grouped[video_name] = []
        grouped[video_name].append(info)
    
    # 按segment_id排序
    for video_name in grouped:
        grouped[video_name].sort(key=lambda x: x['segment_id'])
    
    return grouped

def create_activitynet_annotation(grouped_videos, action_label="custom_action"):
    """
    创建符合ActivityNet格式的annotation文件
    """
    annotation_db = {}
    
    for video_name, segments in grouped_videos.items():
        # 计算整个视频的总时长（从第一个片段开始到最后一个片段结束）
        if not segments:
            continue
            
        first_segment = segments[0]
        last_segment = segments[-1]
        
        # 使用第一个片段的fps作为整个视频的fps
        fps = first_segment['fps']
        
        # 计算总时长和总帧数
        total_duration = last_segment['segment_end_time']
        total_frames = int(total_duration * fps)
        
        # 创建annotations列表
        annotations = []
        for segment in segments:
            annotations.append({
                "segment": [segment['segment_start_time'], segment['segment_end_time']],
                "label": action_label
            })
        
        # 创建视频条目
        annotation_db[video_name] = {
            "duration_second": total_duration,
            "duration_frame": total_frames,
            "annotations": annotations,
            "feature_frame": total_frames,  # 假设特征覆盖所有帧
            "fps": fps,
            "rfps": fps  # 实际fps，这里假设与fps相同
        }
    
    return annotation_db

def main():
    # 设置路径
    video_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/segmented_videos_ok"
    output_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data"
    
    print("开始分析视频文件...")
    
    # 分析视频
    video_info_list = analyze_video_directory(video_dir)
    print(f"成功分析了 {len(video_info_list)} 个视频文件")
    
    # 保存详细信息到CSV
    df = pd.DataFrame(video_info_list)
    csv_path = os.path.join(output_dir, "video_analysis.csv")
    df.to_csv(csv_path, index=False)
    print(f"详细信息已保存到: {csv_path}")
    
    # 按原始视频分组
    grouped_videos = group_videos_by_original(video_info_list)
    print(f"发现 {len(grouped_videos)} 个原始视频，包含 {len(video_info_list)} 个片段")
    
    # 创建ActivityNet格式的annotation
    annotation_db = create_activitynet_annotation(grouped_videos)
    
    # 保存annotation文件
    annotation_path = os.path.join(output_dir, "custom_tad_annotations.json")
    with open(annotation_path, 'w', encoding='utf-8') as f:
        json.dump(annotation_db, f, indent=2, ensure_ascii=False)
    
    print(f"Annotation文件已保存到: {annotation_path}")
    
    # 打印统计信息
    print("\n=== 统计信息 ===")
    for video_name, segments in grouped_videos.items():
        print(f"视频: {video_name}")
        print(f"  片段数量: {len(segments)}")
        print(f"  总时长: {segments[-1]['segment_end_time']:.2f}秒")
        print(f"  FPS: {segments[0]['fps']:.2f}")
        print()

if __name__ == "__main__":
    main()
