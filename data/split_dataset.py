#!/usr/bin/env python3
"""
将数据集分割为训练集和验证集
"""

import json
import random
from pathlib import Path

def split_dataset(annotation_file, train_ratio=0.8):
    """
    将数据集分割为训练集和验证集
    """
    with open(annotation_file, 'r') as f:
        data = json.load(f)
    
    # 获取所有视频名称
    video_names = list(data.keys())
    random.shuffle(video_names)
    
    # 计算分割点
    split_point = int(len(video_names) * train_ratio)
    
    # 分割数据
    train_videos = video_names[:split_point]
    val_videos = video_names[split_point:]
    
    # 创建训练集和验证集
    train_data = {name: data[name] for name in train_videos}
    val_data = {name: data[name] for name in val_videos}
    
    return train_data, val_data

def main():
    # 设置随机种子
    random.seed(42)
    
    # 输入和输出路径
    input_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/custom_tad_annotations.json"
    output_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/CustomTAD"
    
    # 分割数据集
    train_data, val_data = split_dataset(input_file, train_ratio=0.8)
    
    # 保存训练集
    train_file = f"{output_dir}/custom_tad_train.json"
    with open(train_file, 'w') as f:
        json.dump(train_data, f, indent=2)
    
    # 保存验证集
    val_file = f"{output_dir}/custom_tad_val.json"
    with open(val_file, 'w') as f:
        json.dump(val_data, f, indent=2)
    
    print(f"数据集分割完成:")
    print(f"训练集: {len(train_data)} 个视频 -> {train_file}")
    print(f"验证集: {len(val_data)} 个视频 -> {val_file}")
    
    # 统计信息
    train_segments = sum(len(video['annotations']) for video in train_data.values())
    val_segments = sum(len(video['annotations']) for video in val_data.values())
    
    print(f"训练集片段数: {train_segments}")
    print(f"验证集片段数: {val_segments}")

if __name__ == "__main__":
    main()
