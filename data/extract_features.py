#!/usr/bin/env python3
"""
从视频中提取特征用于TAD任务
使用预训练的模型提取视频特征并保存为CSV格式
"""

import os
import sys
import cv2
import torch
import numpy as np
import pandas as pd
from pathlib import Path
import json
from tqdm import tqdm

# 添加mmaction2路径
sys.path.insert(0, '/home/<USER>/johnny_ws/mmaction2_ws/mmaction2')

from mmaction.apis import init_recognizer, inference_recognizer
from mmaction.utils import register_all_modules

def extract_video_features_with_model(video_path, model, device='cuda'):
    """
    使用预训练模型提取视频特征
    """
    try:
        # 读取视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"无法打开视频: {video_path}")
            return None
            
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 读取所有帧
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        cap.release()
        
        if len(frames) == 0:
            return None
            
        # 使用模型进行推理并提取特征
        # 这里我们使用一个简化的方法，实际应该使用MMAction2的特征提取API
        result = inference_recognizer(model, video_path)
        
        # 提取特征向量（这里需要根据具体模型调整）
        # 对于TAD任务，通常需要时序特征
        features = extract_temporal_features(frames, model, device)
        
        return features
        
    except Exception as e:
        print(f"提取特征时出错 {video_path}: {e}")
        return None

def extract_temporal_features(frames, model, device, segment_length=16):
    """
    提取时序特征
    """
    # 简化的特征提取，实际应该使用更复杂的方法
    features = []
    
    # 将视频分成多个段落
    num_frames = len(frames)
    num_segments = max(1, num_frames // segment_length)
    
    for i in range(num_segments):
        start_idx = i * segment_length
        end_idx = min((i + 1) * segment_length, num_frames)
        
        # 提取该段落的特征（这里使用简化的方法）
        segment_frames = frames[start_idx:end_idx]
        
        # 计算简单的视觉特征（实际应该使用深度学习模型）
        segment_feature = compute_simple_features(segment_frames)
        features.append(segment_feature)
    
    return np.array(features)

def compute_simple_features(frames):
    """
    计算简单的视觉特征作为示例
    实际应用中应该使用预训练的深度学习模型
    """
    if not frames:
        return np.zeros(400)  # 返回400维特征向量

    # 计算颜色直方图特征
    hist_features = []
    for frame in frames:
        # RGB直方图
        for channel in range(3):
            hist = cv2.calcHist([frame], [channel], None, [32], [0, 256])
            hist_features.extend(hist.flatten())

    # 计算简单的统计特征替代光流
    if len(frames) > 1:
        motion_features = []
        for i in range(len(frames) - 1):
            gray1 = cv2.cvtColor(frames[i], cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(frames[i + 1], cv2.COLOR_BGR2GRAY)

            # 计算帧差
            diff = cv2.absdiff(gray1, gray2)
            motion_features.append(np.mean(diff))

        # 填充到固定长度
        while len(motion_features) < 50:
            motion_features.append(0)
        motion_features = motion_features[:50]
    else:
        motion_features = [0] * 50

    # 合并特征
    all_features = hist_features + motion_features

    # 填充或截断到400维
    if len(all_features) < 400:
        all_features.extend([0] * (400 - len(all_features)))
    else:
        all_features = all_features[:400]

    return np.array(all_features, dtype=np.float32)

def create_feature_csv_for_video(video_name, grouped_videos, video_dir, output_dir):
    """
    为单个原始视频创建特征CSV文件
    """
    segments = grouped_videos[video_name]
    
    # 按时间顺序排序
    segments.sort(key=lambda x: x['segment_start_time'])
    
    # 创建时间轴上的特征
    # 假设我们要创建100个时间步的特征
    num_timesteps = 100
    feature_dim = 400
    
    # 获取视频总时长
    total_duration = segments[-1]['segment_end_time']
    
    # 创建时间步
    timestep_duration = total_duration / num_timesteps
    
    # 初始化特征矩阵
    features = np.zeros((num_timesteps, feature_dim))
    
    # 为每个时间步分配特征
    for t in range(num_timesteps):
        timestep_start = t * timestep_duration
        timestep_end = (t + 1) * timestep_duration
        timestep_center = (timestep_start + timestep_end) / 2
        
        # 找到包含此时间步的片段
        relevant_segments = []
        for segment in segments:
            if (segment['segment_start_time'] <= timestep_center <= segment['segment_end_time']):
                relevant_segments.append(segment)
        
        if relevant_segments:
            # 如果有相关片段，提取特征
            segment = relevant_segments[0]  # 使用第一个相关片段
            video_path = os.path.join(video_dir, segment['filename'])
            
            if os.path.exists(video_path):
                # 提取简单特征
                cap = cv2.VideoCapture(video_path)
                frames = []
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    frames.append(frame)
                cap.release()
                
                if frames:
                    segment_features = compute_simple_features(frames)
                    features[t] = segment_features
        else:
            # 如果没有相关片段，使用零特征或插值
            features[t] = np.zeros(feature_dim)
    
    # 保存为CSV
    csv_filename = f"{video_name}.csv"
    csv_path = os.path.join(output_dir, csv_filename)
    
    # 创建DataFrame
    df = pd.DataFrame(features)
    df.to_csv(csv_path, header=False, index=False)
    
    print(f"特征已保存到: {csv_path}")
    return csv_path

def main():
    # 设置路径
    video_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/segmented_videos_ok"
    annotation_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/custom_tad_annotations.json"
    output_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/features"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取annotation文件
    with open(annotation_file, 'r') as f:
        annotation_db = json.load(f)
    
    # 读取视频分析结果
    analysis_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/video_analysis.csv"
    df = pd.read_csv(analysis_file)
    
    # 按原始视频分组
    grouped_videos = {}
    for _, row in df.iterrows():
        video_name = row['video_name']
        if video_name not in grouped_videos:
            grouped_videos[video_name] = []
        grouped_videos[video_name].append(row.to_dict())
    
    print(f"开始为 {len(grouped_videos)} 个视频提取特征...")
    
    # 为每个视频创建特征文件
    for video_name in tqdm(grouped_videos.keys()):
        print(f"\n处理视频: {video_name}")
        csv_path = create_feature_csv_for_video(
            video_name, grouped_videos, video_dir, output_dir
        )
    
    print(f"\n所有特征已提取完成，保存在: {output_dir}")

if __name__ == "__main__":
    main()
