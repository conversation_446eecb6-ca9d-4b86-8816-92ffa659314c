2025/07/29 14:29:42 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.20 (default, Oct  3 2024, 15:24:27) [GCC 11.2.0]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 285836
    GPU 0: NVIDIA RTX A5000
    CUDA_HOME: /usr
    NVCC: Cuda compilation tools, release 12.0, V12.0.140
    GCC: gcc (Ubuntu 13.3.0-6ubuntu2~24.04) 13.3.0
    PyTorch: 2.4.1+cu121
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201703
  - Intel(R) oneAPI Math Kernel Library Version 2022.2-Product Build 20220804 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.4.2 (Git Hash 1137e04ec0b5251ca2b4400a4fd3c667ce843d67)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX512
  - CUDA Runtime 12.1
  - NVCC architecture flags: -gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90
  - CuDNN 90.1  (built against CUDA 12.4)
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=12.1, CUDNN_VERSION=9.1.0, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -D_GLIBCXX_USE_CXX11_ABI=0 -fabi-version=11 -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DUSE_FBGEMM -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -O2 -fPIC -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Werror=bool-operation -Wnarrowing -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-stringop-overflow -Wsuggest-override -Wno-psabi -Wno-error=pedantic -Wno-error=old-style-cast -Wno-missing-braces -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=2.4.1, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=1, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=1, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, 

    TorchVision: 0.19.1+cu121
    OpenCV: 4.12.0
    MMEngine: 0.10.7

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 285836
    diff_rank_seed: False
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/07/29 14:29:42 - mmengine - INFO - Config:
ann_file_test = 'data/MultiClassTAD/multiclass_tad_val.json'
ann_file_train = 'data/MultiClassTAD/multiclass_tad_train.json'
ann_file_val = 'data/MultiClassTAD/multiclass_tad_val.json'
class_names = [
    'nok_appearance_defect',
    'nok_electric_defect',
    'ok',
]
data_root = 'data/MultiClassTAD/features/'
data_root_val = 'data/MultiClassTAD/features/'
dataset_type = 'ActivityNetDataset'
default_hooks = dict(
    checkpoint=dict(interval=5, save_best='auto', type='CheckpointHook'),
    logger=dict(ignore_last=False, interval=10, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))
default_scope = 'mmaction'
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
launcher = 'none'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
max_epochs = 30
model = dict(
    boundary_ratio=0.5,
    feat_dim=400,
    num_samples=32,
    num_samples_per_bin=3,
    post_process_top_k=100,
    soft_nms_alpha=0.4,
    soft_nms_high_threshold=0.9,
    soft_nms_low_threshold=0.5,
    temporal_dim=16,
    type='BMN')
num_classes = 3
optim_wrapper = dict(
    clip_grad=dict(max_norm=40, norm_type=2),
    optimizer=dict(lr=0.0005, type='Adam', weight_decay=0.0001))
param_scheduler = [
    dict(
        begin=0,
        by_epoch=True,
        end=30,
        gamma=0.1,
        milestones=[
            20,
            25,
        ],
        type='MultiStepLR'),
]
randomness = dict(deterministic=False, diff_rank_seed=False, seed=None)
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=1,
    dataset=dict(
        ann_file='data/MultiClassTAD/multiclass_tad_val.json',
        data_prefix=dict(video='data/MultiClassTAD/features/'),
        pipeline=[
            dict(type='LoadLocalizationFeature'),
            dict(
                interpolation_mode='linear',
                temporal_dim=16,
                type='ResizeLocalizationFeature'),
            dict(
                keys=('gt_bbox', ),
                meta_keys=(
                    'video_name',
                    'duration_second',
                    'duration_frame',
                    'annotations',
                    'feature_frame',
                ),
                type='PackLocalizationInputs'),
        ],
        test_mode=True,
        type='ActivityNetDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    dump_config=dict(
        out='./work_dirs/bmn_multiclass_tad//results.json',
        output_format='json'),
    metric_type='AR@AN',
    type='ANetMetric')
test_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        interpolation_mode='linear',
        temporal_dim=16,
        type='ResizeLocalizationFeature'),
    dict(
        keys=('gt_bbox', ),
        meta_keys=(
            'video_name',
            'duration_second',
            'duration_frame',
            'annotations',
            'feature_frame',
        ),
        type='PackLocalizationInputs'),
]
train_cfg = dict(
    max_epochs=30, type='EpochBasedTrainLoop', val_begin=1, val_interval=3)
train_dataloader = dict(
    batch_size=4,
    dataset=dict(
        ann_file='data/MultiClassTAD/multiclass_tad_train.json',
        data_prefix=dict(video='data/MultiClassTAD/features/'),
        pipeline=[
            dict(type='LoadLocalizationFeature'),
            dict(
                interpolation_mode='linear',
                temporal_dim=16,
                type='ResizeLocalizationFeature'),
            dict(type='GenerateLocalizationLabels'),
            dict(
                keys=('gt_bbox', ),
                meta_keys=('video_name', ),
                type='PackLocalizationInputs'),
        ],
        type='ActivityNetDataset'),
    drop_last=True,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        interpolation_mode='linear',
        temporal_dim=16,
        type='ResizeLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        keys=('gt_bbox', ),
        meta_keys=('video_name', ),
        type='PackLocalizationInputs'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=1,
    dataset=dict(
        ann_file='data/MultiClassTAD/multiclass_tad_val.json',
        data_prefix=dict(video='data/MultiClassTAD/features/'),
        pipeline=[
            dict(type='LoadLocalizationFeature'),
            dict(
                interpolation_mode='linear',
                temporal_dim=16,
                type='ResizeLocalizationFeature'),
            dict(type='GenerateLocalizationLabels'),
            dict(
                keys=('gt_bbox', ),
                meta_keys=(
                    'video_name',
                    'duration_second',
                    'duration_frame',
                    'annotations',
                    'feature_frame',
                ),
                type='PackLocalizationInputs'),
        ],
        test_mode=True,
        type='ActivityNetDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    dump_config=dict(
        out='./work_dirs/bmn_multiclass_tad//results.json',
        output_format='json'),
    metric_type='AR@AN',
    type='ANetMetric')
val_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        interpolation_mode='linear',
        temporal_dim=16,
        type='ResizeLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        keys=('gt_bbox', ),
        meta_keys=(
            'video_name',
            'duration_second',
            'duration_frame',
            'annotations',
            'feature_frame',
        ),
        type='PackLocalizationInputs'),
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='ActionVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = './work_dirs/test_bmn_multiclass_tad'

2025/07/29 14:29:43 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/07/29 14:29:43 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SyncBuffersHook                    
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SyncBuffersHook                    
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2025/07/29 14:29:44 - mmengine - WARNING - The prefix is not set in metric class ANetMetric.
2025/07/29 14:29:44 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/07/29 14:29:44 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/07/29 14:29:44 - mmengine - INFO - Checkpoints will be saved to /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/test_bmn_multiclass_tad.
2025/07/29 14:29:45 - mmengine - INFO - Epoch(train)  [1][10/26]  lr: 5.0000e-04  eta: 0:00:56  time: 0.0732  data_time: 0.0128  memory: 223  grad_norm: 37.6622  loss: 16.3701
2025/07/29 14:29:45 - mmengine - INFO - Epoch(train)  [1][20/26]  lr: 5.0000e-04  eta: 0:00:34  time: 0.0452  data_time: 0.0077  memory: 223  grad_norm: 18.8348  loss: 15.9990
2025/07/29 14:29:45 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:45 - mmengine - INFO - Epoch(train)  [1][26/26]  lr: 5.0000e-04  eta: 0:00:29  time: 0.0173  data_time: 0.0026  memory: 223  grad_norm: 0.0037  loss: 15.7166
2025/07/29 14:29:45 - mmengine - INFO - Epoch(train)  [2][10/26]  lr: 5.0000e-04  eta: 0:00:24  time: 0.0185  data_time: 0.0033  memory: 223  grad_norm: 0.0074  loss: 15.5698
2025/07/29 14:29:45 - mmengine - INFO - Epoch(train)  [2][20/26]  lr: 5.0000e-04  eta: 0:00:22  time: 0.0184  data_time: 0.0032  memory: 223  grad_norm: 0.0037  loss: 15.5172
2025/07/29 14:29:46 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:46 - mmengine - INFO - Epoch(train)  [2][26/26]  lr: 5.0000e-04  eta: 0:00:20  time: 0.0173  data_time: 0.0024  memory: 223  grad_norm: 0.0000  loss: 15.5384
2025/07/29 14:29:46 - mmengine - INFO - Epoch(train)  [3][10/26]  lr: 5.0000e-04  eta: 0:00:19  time: 0.0184  data_time: 0.0034  memory: 223  grad_norm: 0.0050  loss: 15.4631
2025/07/29 14:29:46 - mmengine - INFO - Epoch(train)  [3][20/26]  lr: 5.0000e-04  eta: 0:00:18  time: 0.0185  data_time: 0.0034  memory: 223  grad_norm: 15.0520  loss: 14.8155
2025/07/29 14:29:46 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:46 - mmengine - INFO - Epoch(train)  [3][26/26]  lr: 5.0000e-04  eta: 0:00:17  time: 0.0174  data_time: 0.0025  memory: 223  grad_norm: 15.0520  loss: 14.8876
2025/07/29 14:29:46 - mmengine - INFO - Epoch(val)  [3][10/27]    eta: 0:00:00  time: 0.0164  data_time: 0.0129  memory: 106  
2025/07/29 14:29:46 - mmengine - INFO - Epoch(val)  [3][20/27]    eta: 0:00:00  time: 0.0096  data_time: 0.0069  memory: 106  
2025/07/29 14:29:46 - mmengine - INFO - Epoch(val) [3][27/27]    auc: 67.4667  AR@1: 0.6815  AR@5: 0.6815  AR@10: 0.6815  AR@100: 0.6815  data_time: 0.0053  time: 0.0078
2025/07/29 14:29:47 - mmengine - INFO - The best checkpoint with 67.4667 auc at 3 epoch is saved to best_auc_epoch_3.pth.
2025/07/29 14:29:47 - mmengine - INFO - Epoch(train)  [4][10/26]  lr: 5.0000e-04  eta: 0:00:16  time: 0.0185  data_time: 0.0036  memory: 223  grad_norm: 0.0036  loss: 15.6559
2025/07/29 14:29:47 - mmengine - INFO - Epoch(train)  [4][20/26]  lr: 5.0000e-04  eta: 0:00:16  time: 0.0183  data_time: 0.0037  memory: 223  grad_norm: 0.0036  loss: 15.7472
2025/07/29 14:29:47 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:47 - mmengine - INFO - Epoch(train)  [4][26/26]  lr: 5.0000e-04  eta: 0:00:15  time: 0.0170  data_time: 0.0027  memory: 223  grad_norm: 0.0000  loss: 15.8172
2025/07/29 14:29:47 - mmengine - INFO - Epoch(train)  [5][10/26]  lr: 5.0000e-04  eta: 0:00:15  time: 0.0178  data_time: 0.0033  memory: 223  grad_norm: 0.0035  loss: 15.7447
2025/07/29 14:29:47 - mmengine - INFO - Epoch(train)  [5][20/26]  lr: 5.0000e-04  eta: 0:00:14  time: 0.0179  data_time: 0.0033  memory: 223  grad_norm: 0.0035  loss: 15.7362
2025/07/29 14:29:47 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:47 - mmengine - INFO - Epoch(train)  [5][26/26]  lr: 5.0000e-04  eta: 0:00:14  time: 0.0168  data_time: 0.0023  memory: 223  grad_norm: 0.0035  loss: 15.7459
2025/07/29 14:29:47 - mmengine - INFO - Saving checkpoint at 5 epochs
2025/07/29 14:29:48 - mmengine - INFO - Epoch(train)  [6][10/26]  lr: 5.0000e-04  eta: 0:00:14  time: 0.0177  data_time: 0.0033  memory: 223  grad_norm: 0.0035  loss: 15.7349
2025/07/29 14:29:48 - mmengine - INFO - Epoch(train)  [6][20/26]  lr: 5.0000e-04  eta: 0:00:13  time: 0.0178  data_time: 0.0033  memory: 223  grad_norm: 0.0035  loss: 15.7456
2025/07/29 14:29:48 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:48 - mmengine - INFO - Epoch(train)  [6][26/26]  lr: 5.0000e-04  eta: 0:00:13  time: 0.0164  data_time: 0.0022  memory: 223  grad_norm: 0.0000  loss: 15.8104
2025/07/29 14:29:48 - mmengine - INFO - Epoch(val)  [6][10/27]    eta: 0:00:00  time: 0.0033  data_time: 0.0012  memory: 106  
2025/07/29 14:29:48 - mmengine - INFO - Epoch(val)  [6][20/27]    eta: 0:00:00  time: 0.0034  data_time: 0.0012  memory: 106  
2025/07/29 14:29:48 - mmengine - INFO - Epoch(val) [6][27/27]    auc: 67.4667  AR@1: 0.6815  AR@5: 0.6815  AR@10: 0.6815  AR@100: 0.6815  data_time: 0.0011  time: 0.0032
2025/07/29 14:29:49 - mmengine - INFO - Epoch(train)  [7][10/26]  lr: 5.0000e-04  eta: 0:00:13  time: 0.0186  data_time: 0.0033  memory: 223  grad_norm: 0.0000  loss: 15.8011
2025/07/29 14:29:49 - mmengine - INFO - Epoch(train)  [7][20/26]  lr: 5.0000e-04  eta: 0:00:12  time: 0.0191  data_time: 0.0035  memory: 223  grad_norm: 0.0000  loss: 15.7955
2025/07/29 14:29:49 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:49 - mmengine - INFO - Epoch(train)  [7][26/26]  lr: 5.0000e-04  eta: 0:00:12  time: 0.0182  data_time: 0.0027  memory: 223  grad_norm: 0.0035  loss: 15.7388
2025/07/29 14:29:49 - mmengine - INFO - Epoch(train)  [8][10/26]  lr: 5.0000e-04  eta: 0:00:12  time: 0.0185  data_time: 0.0029  memory: 223  grad_norm: 781.9306  loss: 15.3329
2025/07/29 14:29:49 - mmengine - INFO - Epoch(train)  [8][20/26]  lr: 5.0000e-04  eta: 0:00:12  time: 0.0190  data_time: 0.0030  memory: 223  grad_norm: 781.9307  loss: 15.4003
2025/07/29 14:29:49 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:49 - mmengine - INFO - Epoch(train)  [8][26/26]  lr: 5.0000e-04  eta: 0:00:11  time: 0.0181  data_time: 0.0023  memory: 223  grad_norm: 781.9307  loss: 15.3897
2025/07/29 14:29:50 - mmengine - INFO - Epoch(train)  [9][10/26]  lr: 5.0000e-04  eta: 0:00:11  time: 0.0186  data_time: 0.0029  memory: 223  grad_norm: 0.0036  loss: 15.8096
2025/07/29 14:29:50 - mmengine - INFO - Epoch(train)  [9][20/26]  lr: 5.0000e-04  eta: 0:00:11  time: 0.0189  data_time: 0.0030  memory: 223  grad_norm: 0.0000  loss: 15.7978
2025/07/29 14:29:50 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:50 - mmengine - INFO - Epoch(train)  [9][26/26]  lr: 5.0000e-04  eta: 0:00:11  time: 0.0182  data_time: 0.0024  memory: 223  grad_norm: 0.0037  loss: 15.7863
2025/07/29 14:29:50 - mmengine - INFO - Epoch(val)  [9][10/27]    eta: 0:00:00  time: 0.0034  data_time: 0.0011  memory: 106  
2025/07/29 14:29:50 - mmengine - INFO - Epoch(val)  [9][20/27]    eta: 0:00:00  time: 0.0036  data_time: 0.0012  memory: 106  
2025/07/29 14:29:50 - mmengine - INFO - Epoch(val) [9][27/27]    auc: 67.4667  AR@1: 0.6815  AR@5: 0.6815  AR@10: 0.6815  AR@100: 0.6815  data_time: 0.0011  time: 0.0035
2025/07/29 14:29:50 - mmengine - INFO - Epoch(train) [10][10/26]  lr: 5.0000e-04  eta: 0:00:10  time: 0.0185  data_time: 0.0032  memory: 223  grad_norm: 0.0037  loss: 15.8104
2025/07/29 14:29:50 - mmengine - INFO - Epoch(train) [10][20/26]  lr: 5.0000e-04  eta: 0:00:10  time: 0.0182  data_time: 0.0032  memory: 223  grad_norm: 0.0038  loss: 15.8081
2025/07/29 14:29:51 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:51 - mmengine - INFO - Epoch(train) [10][26/26]  lr: 5.0000e-04  eta: 0:00:10  time: 0.0170  data_time: 0.0023  memory: 223  grad_norm: 0.0038  loss: 15.7948
2025/07/29 14:29:51 - mmengine - INFO - Saving checkpoint at 10 epochs
2025/07/29 14:29:51 - mmengine - INFO - Epoch(train) [11][10/26]  lr: 5.0000e-04  eta: 0:00:10  time: 0.0179  data_time: 0.0032  memory: 223  grad_norm: 0.0039  loss: 15.7850
2025/07/29 14:29:51 - mmengine - INFO - Epoch(train) [11][20/26]  lr: 5.0000e-04  eta: 0:00:10  time: 0.0184  data_time: 0.0033  memory: 223  grad_norm: 0.0039  loss: 15.7991
2025/07/29 14:29:51 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:51 - mmengine - INFO - Epoch(train) [11][26/26]  lr: 5.0000e-04  eta: 0:00:09  time: 0.0175  data_time: 0.0025  memory: 223  grad_norm: 0.0000  loss: 15.8030
2025/07/29 14:29:52 - mmengine - INFO - Epoch(train) [12][10/26]  lr: 5.0000e-04  eta: 0:00:09  time: 0.0180  data_time: 0.0033  memory: 223  grad_norm: 0.0000  loss: 15.7897
2025/07/29 14:29:52 - mmengine - INFO - Epoch(train) [12][20/26]  lr: 5.0000e-04  eta: 0:00:09  time: 0.0181  data_time: 0.0032  memory: 223  grad_norm: 0.0040  loss: 15.7869
2025/07/29 14:29:52 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:52 - mmengine - INFO - Epoch(train) [12][26/26]  lr: 5.0000e-04  eta: 0:00:09  time: 0.0170  data_time: 0.0023  memory: 223  grad_norm: 0.0040  loss: 15.7842
2025/07/29 14:29:52 - mmengine - INFO - Epoch(val) [12][10/27]    eta: 0:00:00  time: 0.0038  data_time: 0.0015  memory: 106  
2025/07/29 14:29:52 - mmengine - INFO - Epoch(val) [12][20/27]    eta: 0:00:00  time: 0.0039  data_time: 0.0015  memory: 106  
2025/07/29 14:29:52 - mmengine - INFO - Epoch(val) [12][27/27]    auc: 67.4667  AR@1: 0.6815  AR@5: 0.6815  AR@10: 0.6815  AR@100: 0.6815  data_time: 0.0014  time: 0.0038
2025/07/29 14:29:52 - mmengine - INFO - Epoch(train) [13][10/26]  lr: 5.0000e-04  eta: 0:00:09  time: 0.0181  data_time: 0.0032  memory: 223  grad_norm: 0.0000  loss: 15.7661
2025/07/29 14:29:52 - mmengine - INFO - Epoch(train) [13][20/26]  lr: 5.0000e-04  eta: 0:00:08  time: 0.0183  data_time: 0.0032  memory: 223  grad_norm: 0.0042  loss: 15.7770
2025/07/29 14:29:52 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:52 - mmengine - INFO - Epoch(train) [13][26/26]  lr: 5.0000e-04  eta: 0:00:08  time: 0.0171  data_time: 0.0023  memory: 223  grad_norm: 0.0042  loss: 15.7941
2025/07/29 14:29:53 - mmengine - INFO - Epoch(train) [14][10/26]  lr: 5.0000e-04  eta: 0:00:08  time: 0.0180  data_time: 0.0033  memory: 223  grad_norm: 0.0000  loss: 15.8105
2025/07/29 14:29:53 - mmengine - INFO - Epoch(train) [14][20/26]  lr: 5.0000e-04  eta: 0:00:08  time: 0.0181  data_time: 0.0034  memory: 223  grad_norm: 0.0000  loss: 15.8090
2025/07/29 14:29:53 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:53 - mmengine - INFO - Epoch(train) [14][26/26]  lr: 5.0000e-04  eta: 0:00:08  time: 0.0169  data_time: 0.0023  memory: 223  grad_norm: 0.0043  loss: 15.8114
2025/07/29 14:29:53 - mmengine - INFO - Epoch(train) [15][10/26]  lr: 5.0000e-04  eta: 0:00:07  time: 0.0177  data_time: 0.0032  memory: 223  grad_norm: 0.0043  loss: 15.7856
2025/07/29 14:29:53 - mmengine - INFO - Epoch(train) [15][20/26]  lr: 5.0000e-04  eta: 0:00:07  time: 0.0187  data_time: 0.0035  memory: 223  grad_norm: 0.0000  loss: 15.7640
2025/07/29 14:29:53 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:53 - mmengine - INFO - Epoch(train) [15][26/26]  lr: 5.0000e-04  eta: 0:00:07  time: 0.0188  data_time: 0.0028  memory: 223  grad_norm: 0.0043  loss: 15.7781
2025/07/29 14:29:53 - mmengine - INFO - Saving checkpoint at 15 epochs
2025/07/29 14:29:54 - mmengine - INFO - Epoch(val) [15][10/27]    eta: 0:00:00  time: 0.0042  data_time: 0.0015  memory: 106  
2025/07/29 14:29:54 - mmengine - INFO - Epoch(val) [15][20/27]    eta: 0:00:00  time: 0.0043  data_time: 0.0015  memory: 106  
2025/07/29 14:29:54 - mmengine - INFO - Epoch(val) [15][27/27]    auc: 67.4667  AR@1: 0.6815  AR@5: 0.6815  AR@10: 0.6815  AR@100: 0.6815  data_time: 0.0014  time: 0.0041
2025/07/29 14:29:54 - mmengine - INFO - Epoch(train) [16][10/26]  lr: 5.0000e-04  eta: 0:00:07  time: 0.0193  data_time: 0.0035  memory: 223  grad_norm: 0.0043  loss: 15.7905
2025/07/29 14:29:54 - mmengine - INFO - Epoch(train) [16][20/26]  lr: 5.0000e-04  eta: 0:00:07  time: 0.0181  data_time: 0.0033  memory: 223  grad_norm: 0.0000  loss: 15.7839
2025/07/29 14:29:54 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:54 - mmengine - INFO - Epoch(train) [16][26/26]  lr: 5.0000e-04  eta: 0:00:07  time: 0.0169  data_time: 0.0024  memory: 223  grad_norm: 0.0044  loss: 15.7788
2025/07/29 14:29:55 - mmengine - INFO - Epoch(train) [17][10/26]  lr: 5.0000e-04  eta: 0:00:06  time: 0.0174  data_time: 0.0030  memory: 223  grad_norm: 0.0044  loss: 15.7899
2025/07/29 14:29:55 - mmengine - INFO - Epoch(train) [17][20/26]  lr: 5.0000e-04  eta: 0:00:06  time: 0.0179  data_time: 0.0031  memory: 223  grad_norm: 0.0043  loss: 15.7812
2025/07/29 14:29:55 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:55 - mmengine - INFO - Epoch(train) [17][26/26]  lr: 5.0000e-04  eta: 0:00:06  time: 0.0170  data_time: 0.0024  memory: 223  grad_norm: 0.0043  loss: 15.7791
2025/07/29 14:29:55 - mmengine - INFO - Epoch(train) [18][10/26]  lr: 5.0000e-04  eta: 0:00:06  time: 0.0176  data_time: 0.0030  memory: 223  grad_norm: 0.0043  loss: 15.7870
2025/07/29 14:29:55 - mmengine - INFO - Epoch(train) [18][20/26]  lr: 5.0000e-04  eta: 0:00:06  time: 0.0175  data_time: 0.0029  memory: 223  grad_norm: 0.0042  loss: 15.8036
2025/07/29 14:29:55 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:55 - mmengine - INFO - Epoch(train) [18][26/26]  lr: 5.0000e-04  eta: 0:00:06  time: 0.0171  data_time: 0.0026  memory: 223  grad_norm: 0.0042  loss: 15.8000
2025/07/29 14:29:55 - mmengine - INFO - Epoch(val) [18][10/27]    eta: 0:00:00  time: 0.0040  data_time: 0.0014  memory: 106  
2025/07/29 14:29:55 - mmengine - INFO - Epoch(val) [18][20/27]    eta: 0:00:00  time: 0.0038  data_time: 0.0014  memory: 106  
2025/07/29 14:29:56 - mmengine - INFO - Epoch(val) [18][27/27]    auc: 67.4667  AR@1: 0.6815  AR@5: 0.6815  AR@10: 0.6815  AR@100: 0.6815  data_time: 0.0013  time: 0.0036
2025/07/29 14:29:56 - mmengine - INFO - Epoch(train) [19][10/26]  lr: 5.0000e-04  eta: 0:00:05  time: 0.0180  data_time: 0.0033  memory: 223  grad_norm: 0.0000  loss: 15.8020
2025/07/29 14:29:56 - mmengine - INFO - Epoch(train) [19][20/26]  lr: 5.0000e-04  eta: 0:00:05  time: 0.0180  data_time: 0.0034  memory: 223  grad_norm: 0.0039  loss: 15.8043
2025/07/29 14:29:56 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:56 - mmengine - INFO - Epoch(train) [19][26/26]  lr: 5.0000e-04  eta: 0:00:05  time: 0.0172  data_time: 0.0026  memory: 223  grad_norm: 0.0039  loss: 15.7973
2025/07/29 14:29:56 - mmengine - INFO - Epoch(train) [20][10/26]  lr: 5.0000e-04  eta: 0:00:05  time: 0.0175  data_time: 0.0029  memory: 223  grad_norm: 0.0039  loss: 15.7981
2025/07/29 14:29:56 - mmengine - INFO - Epoch(train) [20][20/26]  lr: 5.0000e-04  eta: 0:00:05  time: 0.0176  data_time: 0.0030  memory: 223  grad_norm: 0.0036  loss: 15.7907
2025/07/29 14:29:56 - mmengine - INFO - Exp name: bmn_2xb4-16x400-20e_multiclass-tad-feature_20250729_142942
2025/07/29 14:29:56 - mmengine - INFO - Epoch(train) [20][26/26]  lr: 5.0000e-04  eta: 0:00:04  time: 0.0171  data_time: 0.0025  memory: 223  grad_norm: 0.0036  loss: 15.7930
2025/07/29 14:29:56 - mmengine - INFO - Saving checkpoint at 20 epochs
