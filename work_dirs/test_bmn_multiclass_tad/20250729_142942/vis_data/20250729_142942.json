{"lr": 0.0005, "data_time": 0.012774419784545899, "grad_norm": 37.6621994047496, "loss": 16.370138931274415, "time": 0.07323544025421143, "epoch": 1, "iter": 10, "memory": 223, "step": 10}
{"lr": 0.0005, "data_time": 0.007724213600158692, "grad_norm": 18.834798192562324, "loss": 15.998961687088013, "time": 0.04520683288574219, "epoch": 1, "iter": 20, "memory": 223, "step": 20}
{"lr": 0.0005, "data_time": 0.002595973014831543, "grad_norm": 0.0036984901875257493, "loss": 15.716631889343262, "time": 0.01733109951019287, "epoch": 1, "iter": 26, "memory": 223, "step": 26}
{"lr": 0.0005, "data_time": 0.0033051371574401855, "grad_norm": 0.00735088549554348, "loss": 15.569829988479615, "time": 0.018463504314422608, "epoch": 2, "iter": 36, "memory": 223, "step": 36}
{"lr": 0.0005, "data_time": 0.003234124183654785, "grad_norm": 0.003652395308017731, "loss": 15.517226076126098, "time": 0.018376851081848146, "epoch": 2, "iter": 46, "memory": 223, "step": 46}
{"lr": 0.0005, "data_time": 0.0024022817611694335, "grad_norm": 0.0, "loss": 15.538417291641235, "time": 0.01733030080795288, "epoch": 2, "iter": 52, "memory": 223, "step": 52}
{"lr": 0.0005, "data_time": 0.0033806681632995604, "grad_norm": 0.005007632076740265, "loss": 15.463105201721191, "time": 0.018428802490234375, "epoch": 3, "iter": 62, "memory": 223, "step": 62}
{"lr": 0.0005, "data_time": 0.003413546085357666, "grad_norm": 15.051975710690021, "loss": 14.815474557876588, "time": 0.01847468614578247, "epoch": 3, "iter": 72, "memory": 223, "step": 72}
{"lr": 0.0005, "data_time": 0.0025327444076538087, "grad_norm": 15.051975710690021, "loss": 14.887612915039062, "time": 0.017400681972503662, "epoch": 3, "iter": 78, "memory": 223, "step": 78}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.005349450641208225, "time": 0.007810398384376809, "step": 3}
{"lr": 0.0005, "data_time": 0.0036289453506469726, "grad_norm": 0.0035648498684168355, "loss": 15.655865716934205, "time": 0.018489372730255128, "epoch": 4, "iter": 88, "memory": 223, "step": 88}
{"lr": 0.0005, "data_time": 0.0036517977714538574, "grad_norm": 0.003564849868416786, "loss": 15.747182512283326, "time": 0.01825525760650635, "epoch": 4, "iter": 98, "memory": 223, "step": 98}
{"lr": 0.0005, "data_time": 0.0026814937591552734, "grad_norm": 0.0, "loss": 15.817245864868164, "time": 0.016988599300384523, "epoch": 4, "iter": 104, "memory": 223, "step": 104}
{"lr": 0.0005, "data_time": 0.003279280662536621, "grad_norm": 0.0035152211785316467, "loss": 15.744666528701782, "time": 0.01775261163711548, "epoch": 5, "iter": 114, "memory": 223, "step": 114}
{"lr": 0.0005, "data_time": 0.0033295392990112306, "grad_norm": 0.0035152211785316467, "loss": 15.736217594146728, "time": 0.017860686779022215, "epoch": 5, "iter": 124, "memory": 223, "step": 124}
{"lr": 0.0005, "data_time": 0.0022644281387329103, "grad_norm": 0.0035152211785316467, "loss": 15.745864820480346, "time": 0.01677062511444092, "epoch": 5, "iter": 130, "memory": 223, "step": 130}
{"lr": 0.0005, "data_time": 0.0032529115676879885, "grad_norm": 0.003512071445584297, "loss": 15.734898138046265, "time": 0.017745554447174072, "epoch": 6, "iter": 140, "memory": 223, "step": 140}
{"lr": 0.0005, "data_time": 0.003271377086639404, "grad_norm": 0.003512071445584297, "loss": 15.745642471313477, "time": 0.017769169807434083, "epoch": 6, "iter": 150, "memory": 223, "step": 150}
{"lr": 0.0005, "data_time": 0.0022047996520996095, "grad_norm": 0.0, "loss": 15.810368299484253, "time": 0.016449832916259767, "epoch": 6, "iter": 156, "memory": 223, "step": 156}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.0010719895362854004, "time": 0.003200735364641462, "step": 6}
{"lr": 0.0005, "data_time": 0.0033434391021728515, "grad_norm": 0.0, "loss": 15.801094007492065, "time": 0.018634605407714843, "epoch": 7, "iter": 166, "memory": 223, "step": 166}
{"lr": 0.0005, "data_time": 0.0035164237022399904, "grad_norm": 0.0, "loss": 15.79549307823181, "time": 0.019124412536621095, "epoch": 7, "iter": 176, "memory": 223, "step": 176}
{"lr": 0.0005, "data_time": 0.002699029445648193, "grad_norm": 0.003540682420134544, "loss": 15.738829421997071, "time": 0.018179941177368163, "epoch": 7, "iter": 182, "memory": 223, "step": 182}
{"lr": 0.0005, "data_time": 0.002881574630737305, "grad_norm": 781.9305914636701, "loss": 15.332904386520386, "time": 0.018509304523468016, "epoch": 8, "iter": 192, "memory": 223, "step": 192}
{"lr": 0.0005, "data_time": 0.0029527068138122557, "grad_norm": 781.9306519560516, "loss": 15.400277662277222, "time": 0.018974483013153076, "epoch": 8, "iter": 202, "memory": 223, "step": 202}
{"lr": 0.0005, "data_time": 0.002290940284729004, "grad_norm": 781.9306519560516, "loss": 15.38972611427307, "time": 0.018112027645111085, "epoch": 8, "iter": 208, "memory": 223, "step": 208}
{"lr": 0.0005, "data_time": 0.002858757972717285, "grad_norm": 0.0036011748015880585, "loss": 15.809567070007324, "time": 0.018593204021453858, "epoch": 9, "iter": 218, "memory": 223, "step": 218}
{"lr": 0.0005, "data_time": 0.00301285982131958, "grad_norm": 0.0, "loss": 15.797815227508545, "time": 0.01891062259674072, "epoch": 9, "iter": 228, "memory": 223, "step": 228}
{"lr": 0.0005, "data_time": 0.0023703217506408692, "grad_norm": 0.0037012960761785506, "loss": 15.78634204864502, "time": 0.018200373649597167, "epoch": 9, "iter": 234, "memory": 223, "step": 234}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.0011386445590427943, "time": 0.0034878764833722797, "step": 9}
{"lr": 0.0005, "data_time": 0.0032185912132263184, "grad_norm": 0.0037012960761785506, "loss": 15.81038432121277, "time": 0.01852421760559082, "epoch": 10, "iter": 244, "memory": 223, "step": 244}
{"lr": 0.0005, "data_time": 0.003174722194671631, "grad_norm": 0.003789941966533661, "loss": 15.808092403411866, "time": 0.01818504333496094, "epoch": 10, "iter": 254, "memory": 223, "step": 254}
{"lr": 0.0005, "data_time": 0.0022606730461120607, "grad_norm": 0.003789941966533661, "loss": 15.794800853729248, "time": 0.017048704624176025, "epoch": 10, "iter": 260, "memory": 223, "step": 260}
{"lr": 0.0005, "data_time": 0.003189969062805176, "grad_norm": 0.003915046527981758, "loss": 15.784989166259766, "time": 0.017936670780181886, "epoch": 11, "iter": 270, "memory": 223, "step": 270}
{"lr": 0.0005, "data_time": 0.003300464153289795, "grad_norm": 0.003915046527981758, "loss": 15.799072694778442, "time": 0.01841038465499878, "epoch": 11, "iter": 280, "memory": 223, "step": 280}
{"lr": 0.0005, "data_time": 0.002486133575439453, "grad_norm": 0.0, "loss": 15.802972459793091, "time": 0.017513155937194824, "epoch": 11, "iter": 286, "memory": 223, "step": 286}
{"lr": 0.0005, "data_time": 0.0032691478729248045, "grad_norm": 0.0, "loss": 15.789721155166626, "time": 0.018017673492431642, "epoch": 12, "iter": 296, "memory": 223, "step": 296}
{"lr": 0.0005, "data_time": 0.003241300582885742, "grad_norm": 0.004047458618879318, "loss": 15.786890316009522, "time": 0.018083131313323973, "epoch": 12, "iter": 306, "memory": 223, "step": 306}
{"lr": 0.0005, "data_time": 0.0023151636123657227, "grad_norm": 0.004047458618879318, "loss": 15.784223508834838, "time": 0.017036843299865722, "epoch": 12, "iter": 312, "memory": 223, "step": 312}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.00136479309626988, "time": 0.0038145354815891813, "step": 12}
{"lr": 0.0005, "data_time": 0.003183233737945557, "grad_norm": 0.0, "loss": 15.766138315200806, "time": 0.018127954006195067, "epoch": 13, "iter": 322, "memory": 223, "step": 322}
{"lr": 0.0005, "data_time": 0.0031651735305786135, "grad_norm": 0.004159815981984139, "loss": 15.776973152160645, "time": 0.018294358253479005, "epoch": 13, "iter": 332, "memory": 223, "step": 332}
{"lr": 0.0005, "data_time": 0.0023481369018554686, "grad_norm": 0.004159815981984139, "loss": 15.794057083129882, "time": 0.017128574848175048, "epoch": 13, "iter": 338, "memory": 223, "step": 338}
{"lr": 0.0005, "data_time": 0.00328141450881958, "grad_norm": 0.0, "loss": 15.810506534576415, "time": 0.018011975288391113, "epoch": 14, "iter": 348, "memory": 223, "step": 348}
{"lr": 0.0005, "data_time": 0.0034178495407104492, "grad_norm": 0.0, "loss": 15.808993339538574, "time": 0.018087005615234374, "epoch": 14, "iter": 358, "memory": 223, "step": 358}
{"lr": 0.0005, "data_time": 0.0023205399513244627, "grad_norm": 0.004252183437347412, "loss": 15.811424446105956, "time": 0.016908586025238037, "epoch": 14, "iter": 364, "memory": 223, "step": 364}
{"lr": 0.0005, "data_time": 0.003172469139099121, "grad_norm": 0.004252183437347412, "loss": 15.78559489250183, "time": 0.017666876316070557, "epoch": 15, "iter": 374, "memory": 223, "step": 374}
{"lr": 0.0005, "data_time": 0.003450667858123779, "grad_norm": 0.0, "loss": 15.763974571228028, "time": 0.018671596050262453, "epoch": 15, "iter": 384, "memory": 223, "step": 384}
{"lr": 0.0005, "data_time": 0.0027666091918945312, "grad_norm": 0.004335518926382065, "loss": 15.77807970046997, "time": 0.018777406215667723, "epoch": 15, "iter": 390, "memory": 223, "step": 390}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.0014098116329738072, "time": 0.004139389310564313, "step": 15}
{"lr": 0.0005, "data_time": 0.003472411632537842, "grad_norm": 0.004335518926382065, "loss": 15.790462303161622, "time": 0.019254732131958007, "epoch": 16, "iter": 400, "memory": 223, "step": 400}
{"lr": 0.0005, "data_time": 0.003341495990753174, "grad_norm": 0.0, "loss": 15.783885383605957, "time": 0.018074846267700194, "epoch": 16, "iter": 410, "memory": 223, "step": 410}
{"lr": 0.0005, "data_time": 0.0024116039276123047, "grad_norm": 0.0043718662112951275, "loss": 15.778764724731445, "time": 0.016909849643707276, "epoch": 16, "iter": 416, "memory": 223, "step": 416}
{"lr": 0.0005, "data_time": 0.002981412410736084, "grad_norm": 0.0043718662112951275, "loss": 15.78987512588501, "time": 0.017391860485076904, "epoch": 17, "iter": 426, "memory": 223, "step": 426}
{"lr": 0.0005, "data_time": 0.0031436681747436523, "grad_norm": 0.004335170984268189, "loss": 15.781229305267335, "time": 0.017885816097259522, "epoch": 17, "iter": 436, "memory": 223, "step": 436}
{"lr": 0.0005, "data_time": 0.002415180206298828, "grad_norm": 0.004335170984268189, "loss": 15.779126024246215, "time": 0.01704326868057251, "epoch": 17, "iter": 442, "memory": 223, "step": 442}
{"lr": 0.0005, "data_time": 0.0029848337173461912, "grad_norm": 0.004335170984268189, "loss": 15.787013292312622, "time": 0.017628228664398192, "epoch": 18, "iter": 452, "memory": 223, "step": 452}
{"lr": 0.0005, "data_time": 0.0029458999633789062, "grad_norm": 0.004164810851216316, "loss": 15.803631258010864, "time": 0.017477238178253175, "epoch": 18, "iter": 462, "memory": 223, "step": 462}
{"lr": 0.0005, "data_time": 0.00257948637008667, "grad_norm": 0.004164810851216316, "loss": 15.799982452392578, "time": 0.017070353031158447, "epoch": 18, "iter": 468, "memory": 223, "step": 468}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.001256133828844343, "time": 0.003640021596636091, "step": 18}
{"lr": 0.0005, "data_time": 0.0033264875411987303, "grad_norm": 0.0, "loss": 15.801975059509278, "time": 0.017979657649993895, "epoch": 19, "iter": 478, "memory": 223, "step": 478}
{"lr": 0.0005, "data_time": 0.0033646345138549803, "grad_norm": 0.003880053386092186, "loss": 15.804318857192992, "time": 0.01798797845840454, "epoch": 19, "iter": 488, "memory": 223, "step": 488}
{"lr": 0.0005, "data_time": 0.0025756120681762694, "grad_norm": 0.003880053386092186, "loss": 15.797307586669922, "time": 0.01715834140777588, "epoch": 19, "iter": 494, "memory": 223, "step": 494}
{"lr": 0.0005, "data_time": 0.0029481053352355957, "grad_norm": 0.003880053386092186, "loss": 15.798099756240845, "time": 0.017454767227172853, "epoch": 20, "iter": 504, "memory": 223, "step": 504}
{"lr": 0.0005, "data_time": 0.003020322322845459, "grad_norm": 0.0035577412694692613, "loss": 15.790709257125854, "time": 0.017617595195770264, "epoch": 20, "iter": 514, "memory": 223, "step": 514}
{"lr": 0.0005, "data_time": 0.002547764778137207, "grad_norm": 0.0035577412694692613, "loss": 15.793018245697022, "time": 0.01706584692001343, "epoch": 20, "iter": 520, "memory": 223, "step": 520}
{"lr": 5e-05, "data_time": 0.0034798622131347657, "grad_norm": 0.003182123228907585, "loss": 15.79924898147583, "time": 0.017993414402008058, "epoch": 21, "iter": 530, "memory": 223, "step": 530}
{"lr": 5e-05, "data_time": 0.003555738925933838, "grad_norm": 0.003182123228907585, "loss": 15.790732717514038, "time": 0.018282377719879152, "epoch": 21, "iter": 540, "memory": 223, "step": 540}
{"lr": 5e-05, "data_time": 0.0025626420974731445, "grad_norm": 0.0, "loss": 15.789024829864502, "time": 0.01711946725845337, "epoch": 21, "iter": 546, "memory": 223, "step": 546}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.0013339774949210031, "time": 0.0038110528673444477, "step": 21}
{"lr": 5e-05, "data_time": 0.0032518863677978515, "grad_norm": 0.0, "loss": 15.783055925369263, "time": 0.01774367094039917, "epoch": 22, "iter": 556, "memory": 223, "step": 556}
{"lr": 5e-05, "data_time": 0.0033469796180725098, "grad_norm": 0.0, "loss": 15.798485565185548, "time": 0.018099725246429443, "epoch": 22, "iter": 566, "memory": 223, "step": 566}
{"lr": 5e-05, "data_time": 0.0025840401649475098, "grad_norm": 0.0031600087881088258, "loss": 15.790117263793945, "time": 0.017130839824676513, "epoch": 22, "iter": 572, "memory": 223, "step": 572}
{"lr": 5e-05, "data_time": 0.003108310699462891, "grad_norm": 0.0031600087881088258, "loss": 15.792902851104737, "time": 0.01789684295654297, "epoch": 23, "iter": 582, "memory": 223, "step": 582}
{"lr": 5e-05, "data_time": 0.003112173080444336, "grad_norm": 0.0031333621591329575, "loss": 15.810224056243896, "time": 0.0179768443107605, "epoch": 23, "iter": 592, "memory": 223, "step": 592}
{"lr": 5e-05, "data_time": 0.0024468660354614257, "grad_norm": 0.0031333621591329575, "loss": 15.819131422042847, "time": 0.01682138442993164, "epoch": 23, "iter": 598, "memory": 223, "step": 598}
{"lr": 5e-05, "data_time": 0.0032922029495239258, "grad_norm": 0.006239440105855465, "loss": 15.83051838874817, "time": 0.017571330070495605, "epoch": 24, "iter": 608, "memory": 223, "step": 608}
{"lr": 5e-05, "data_time": 0.0033125042915344237, "grad_norm": 0.0031060779467225073, "loss": 15.825119400024414, "time": 0.017687177658081053, "epoch": 24, "iter": 618, "memory": 223, "step": 618}
{"lr": 5e-05, "data_time": 0.002551698684692383, "grad_norm": 0.0, "loss": 15.808728742599488, "time": 0.017138826847076415, "epoch": 24, "iter": 624, "memory": 223, "step": 624}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.0010967510087149484, "time": 0.0035104836736406598, "step": 24}
{"lr": 5e-05, "data_time": 0.00323946475982666, "grad_norm": 0.0030674204230308534, "loss": 15.810662651062012, "time": 0.018200492858886717, "epoch": 25, "iter": 634, "memory": 223, "step": 634}
{"lr": 5e-05, "data_time": 0.003240156173706055, "grad_norm": 0.0030674204230308534, "loss": 15.809720087051392, "time": 0.018114566802978516, "epoch": 25, "iter": 644, "memory": 223, "step": 644}
{"lr": 5e-05, "data_time": 0.0025350213050842284, "grad_norm": 0.0030674204230308534, "loss": 15.793771362304687, "time": 0.017370474338531495, "epoch": 25, "iter": 650, "memory": 223, "step": 650}
{"lr": 5e-06, "data_time": 0.0032652497291564943, "grad_norm": 0.0, "loss": 15.785554838180541, "time": 0.01820739507675171, "epoch": 26, "iter": 660, "memory": 223, "step": 660}
{"lr": 5e-06, "data_time": 0.003384113311767578, "grad_norm": 0.0030391592532396316, "loss": 15.801494550704955, "time": 0.018519878387451172, "epoch": 26, "iter": 670, "memory": 223, "step": 670}
{"lr": 5e-06, "data_time": 0.0026669859886169433, "grad_norm": 0.0030391592532396316, "loss": 15.780774450302124, "time": 0.01746467351913452, "epoch": 26, "iter": 676, "memory": 223, "step": 676}
{"lr": 5e-06, "data_time": 0.0032209038734436037, "grad_norm": 0.0, "loss": 15.775608015060424, "time": 0.018291795253753663, "epoch": 27, "iter": 686, "memory": 223, "step": 686}
{"lr": 5e-06, "data_time": 0.0030283689498901366, "grad_norm": 0.0030367957428097726, "loss": 15.798413228988647, "time": 0.018162095546722413, "epoch": 27, "iter": 696, "memory": 223, "step": 696}
{"lr": 5e-06, "data_time": 0.0025921225547790526, "grad_norm": 0.0030367957428097726, "loss": 15.808950567245484, "time": 0.017340588569641113, "epoch": 27, "iter": 702, "memory": 223, "step": 702}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.0011840803282601492, "time": 0.003607954297746931, "step": 27}
{"lr": 5e-06, "data_time": 0.003440999984741211, "grad_norm": 0.0030339011922478677, "loss": 15.813423919677735, "time": 0.018485057353973388, "epoch": 28, "iter": 712, "memory": 223, "step": 712}
{"lr": 5e-06, "data_time": 0.0033586621284484863, "grad_norm": 0.0030339011922478677, "loss": 15.814500188827514, "time": 0.01853882074356079, "epoch": 28, "iter": 722, "memory": 223, "step": 722}
{"lr": 5e-06, "data_time": 0.002650165557861328, "grad_norm": 0.0030339011922478677, "loss": 15.798277568817138, "time": 0.017353713512420654, "epoch": 28, "iter": 728, "memory": 223, "step": 728}
{"lr": 5e-06, "data_time": 0.003136718273162842, "grad_norm": 0.0, "loss": 15.778480577468873, "time": 0.018125343322753906, "epoch": 29, "iter": 738, "memory": 223, "step": 738}
{"lr": 5e-06, "data_time": 0.0030902743339538572, "grad_norm": 0.0, "loss": 15.764133548736572, "time": 0.018329715728759764, "epoch": 29, "iter": 748, "memory": 223, "step": 748}
{"lr": 5e-06, "data_time": 0.0025821208953857424, "grad_norm": 0.003032146580517292, "loss": 15.77070918083191, "time": 0.017423570156097412, "epoch": 29, "iter": 754, "memory": 223, "step": 754}
{"lr": 5e-06, "data_time": 0.003018975257873535, "grad_norm": 0.003032146580517292, "loss": 15.777968263626098, "time": 0.017863333225250244, "epoch": 30, "iter": 764, "memory": 223, "step": 764}
{"lr": 5e-06, "data_time": 0.0029592514038085938, "grad_norm": 0.0, "loss": 15.78560380935669, "time": 0.017949783802032472, "epoch": 30, "iter": 774, "memory": 223, "step": 774}
{"lr": 5e-06, "data_time": 0.0024168968200683595, "grad_norm": 0.0030296862125396727, "loss": 15.78453187942505, "time": 0.017158281803131104, "epoch": 30, "iter": 780, "memory": 223, "step": 780}
{"auc": 67.46666666666667, "AR@1": 0.6814814814814815, "AR@5": 0.6814814814814815, "AR@10": 0.6814814814814815, "AR@100": 0.6814814814814815, "data_time": 0.0012997303690229142, "time": 0.003668989453996931, "step": 30}
