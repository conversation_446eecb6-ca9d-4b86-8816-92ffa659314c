ann_file_test = 'data/MultiClassTAD/multiclass_tad_val.json'
ann_file_train = 'data/MultiClassTAD/multiclass_tad_train.json'
ann_file_val = 'data/MultiClassTAD/multiclass_tad_val.json'
class_names = [
    'nok_appearance_defect',
    'nok_electric_defect',
    'ok',
]
data_root = 'data/MultiClassTAD/features/'
data_root_val = 'data/MultiClassTAD/features/'
dataset_type = 'ActivityNetDataset'
default_hooks = dict(
    checkpoint=dict(interval=5, save_best='auto', type='CheckpointHook'),
    logger=dict(ignore_last=False, interval=10, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))
default_scope = 'mmaction'
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
launcher = 'none'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
max_epochs = 30
model = dict(
    boundary_ratio=0.5,
    feat_dim=400,
    num_samples=32,
    num_samples_per_bin=3,
    post_process_top_k=100,
    soft_nms_alpha=0.4,
    soft_nms_high_threshold=0.9,
    soft_nms_low_threshold=0.5,
    temporal_dim=16,
    type='BMN')
num_classes = 3
optim_wrapper = dict(
    clip_grad=dict(max_norm=40, norm_type=2),
    optimizer=dict(lr=0.0005, type='Adam', weight_decay=0.0001))
param_scheduler = [
    dict(
        begin=0,
        by_epoch=True,
        end=30,
        gamma=0.1,
        milestones=[
            20,
            25,
        ],
        type='MultiStepLR'),
]
randomness = dict(deterministic=False, diff_rank_seed=False, seed=None)
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=1,
    dataset=dict(
        ann_file='data/MultiClassTAD/multiclass_tad_val.json',
        data_prefix=dict(video='data/MultiClassTAD/features/'),
        pipeline=[
            dict(type='LoadLocalizationFeature'),
            dict(
                interpolation_mode='linear',
                temporal_dim=16,
                type='ResizeLocalizationFeature'),
            dict(
                keys=('gt_bbox', ),
                meta_keys=(
                    'video_name',
                    'duration_second',
                    'duration_frame',
                    'annotations',
                    'feature_frame',
                ),
                type='PackLocalizationInputs'),
        ],
        test_mode=True,
        type='ActivityNetDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    dump_config=dict(
        out='./work_dirs/bmn_multiclass_tad//results.json',
        output_format='json'),
    metric_type='AR@AN',
    type='ANetMetric')
test_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        interpolation_mode='linear',
        temporal_dim=16,
        type='ResizeLocalizationFeature'),
    dict(
        keys=('gt_bbox', ),
        meta_keys=(
            'video_name',
            'duration_second',
            'duration_frame',
            'annotations',
            'feature_frame',
        ),
        type='PackLocalizationInputs'),
]
train_cfg = dict(
    max_epochs=30, type='EpochBasedTrainLoop', val_begin=1, val_interval=3)
train_dataloader = dict(
    batch_size=4,
    dataset=dict(
        ann_file='data/MultiClassTAD/multiclass_tad_train.json',
        data_prefix=dict(video='data/MultiClassTAD/features/'),
        pipeline=[
            dict(type='LoadLocalizationFeature'),
            dict(
                interpolation_mode='linear',
                temporal_dim=16,
                type='ResizeLocalizationFeature'),
            dict(type='GenerateLocalizationLabels'),
            dict(
                keys=('gt_bbox', ),
                meta_keys=('video_name', ),
                type='PackLocalizationInputs'),
        ],
        type='ActivityNetDataset'),
    drop_last=True,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        interpolation_mode='linear',
        temporal_dim=16,
        type='ResizeLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        keys=('gt_bbox', ),
        meta_keys=('video_name', ),
        type='PackLocalizationInputs'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=1,
    dataset=dict(
        ann_file='data/MultiClassTAD/multiclass_tad_val.json',
        data_prefix=dict(video='data/MultiClassTAD/features/'),
        pipeline=[
            dict(type='LoadLocalizationFeature'),
            dict(
                interpolation_mode='linear',
                temporal_dim=16,
                type='ResizeLocalizationFeature'),
            dict(type='GenerateLocalizationLabels'),
            dict(
                keys=('gt_bbox', ),
                meta_keys=(
                    'video_name',
                    'duration_second',
                    'duration_frame',
                    'annotations',
                    'feature_frame',
                ),
                type='PackLocalizationInputs'),
        ],
        test_mode=True,
        type='ActivityNetDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    dump_config=dict(
        out='./work_dirs/bmn_multiclass_tad//results.json',
        output_format='json'),
    metric_type='AR@AN',
    type='ANetMetric')
val_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        interpolation_mode='linear',
        temporal_dim=16,
        type='ResizeLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        keys=('gt_bbox', ),
        meta_keys=(
            'video_name',
            'duration_second',
            'duration_frame',
            'annotations',
            'feature_frame',
        ),
        type='PackLocalizationInputs'),
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='ActionVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = './work_dirs/test_bmn_multiclass_tad'
