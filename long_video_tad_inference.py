#!/usr/bin/env python3
"""
长视频TAD推理脚本
使用训练好的BMN模型对长视频进行时间动作检测
"""

import os
import sys
import cv2
import torch
import numpy as np
import pandas as pd
import json
import argparse
from pathlib import Path
from tqdm import tqdm
import tempfile

# 添加mmaction2路径
sys.path.insert(0, 'mmaction2')

from mmaction.utils import register_all_modules
from mmengine.config import Config
from mmengine.runner import Runner

# 注册所有模块
register_all_modules()

def extract_features_from_video(video_path, output_dir, segment_duration=10.0, overlap=2.0):
    """
    从长视频中提取特征，分段处理
    
    Args:
        video_path: 视频文件路径
        output_dir: 输出目录
        segment_duration: 每段的时长（秒）
        overlap: 段之间的重叠时长（秒）
    
    Returns:
        segments_info: 包含每段信息的列表
    """
    print(f"正在处理视频: {video_path}")
    
    # 获取视频信息
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise ValueError(f"无法打开视频: {video_path}")
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    
    print(f"视频信息: FPS={fps:.2f}, 总帧数={total_frames}, 时长={duration:.2f}秒")
    
    # 计算分段参数
    segment_frames = int(segment_duration * fps)
    overlap_frames = int(overlap * fps)
    step_frames = segment_frames - overlap_frames
    
    segments_info = []
    segment_idx = 0
    
    # 分段提取特征
    for start_frame in range(0, total_frames, step_frames):
        end_frame = min(start_frame + segment_frames, total_frames)
        
        if end_frame - start_frame < segment_frames // 2:
            # 如果最后一段太短，跳过
            break
        
        start_time = start_frame / fps
        end_time = end_frame / fps
        
        print(f"处理段 {segment_idx}: 帧 {start_frame}-{end_frame} (时间 {start_time:.2f}-{end_time:.2f}秒)")
        
        # 提取该段的帧
        frames = []
        cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        
        for _ in range(end_frame - start_frame):
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        
        if len(frames) == 0:
            continue
        
        # 计算特征
        features = compute_segment_features(frames)
        
        # 保存特征文件
        video_name = Path(video_path).stem
        feature_filename = f"{video_name}_seg{segment_idx:03d}.csv"
        feature_path = os.path.join(output_dir, feature_filename)
        
        # 保存为CSV格式
        df = pd.DataFrame(features.T)  # 转置使得每行是一个时间步
        df.to_csv(feature_path, header=False, index=False)
        
        # 记录段信息
        segment_info = {
            'segment_id': segment_idx,
            'video_name': video_name,
            'feature_file': feature_filename,
            'start_time': start_time,
            'end_time': end_time,
            'start_frame': start_frame,
            'end_frame': end_frame,
            'duration': end_time - start_time,
            'feature_frame': features.shape[1]  # 时间步数
        }
        segments_info.append(segment_info)
        
        segment_idx += 1
    
    cap.release()
    print(f"共提取了 {len(segments_info)} 个段落的特征")
    
    return segments_info

def compute_segment_features(frames, target_dim=400):
    """
    计算视频段的特征
    这里使用简化的特征提取方法，实际应用中可以使用预训练的深度学习模型
    """
    if not frames:
        return np.zeros((target_dim, 1))
    
    features = []
    
    # 对每帧计算特征
    for frame in frames:
        # 计算颜色直方图特征
        hist_features = []
        
        # RGB直方图 (每个通道32个bin，共96维)
        for channel in range(3):
            hist = cv2.calcHist([frame], [channel], None, [32], [0, 256])
            hist_features.extend(hist.flatten())
        
        # 计算纹理特征 (简化的LBP特征，100维)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        # 简化的纹理特征：使用梯度统计
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        
        texture_features = [
            np.mean(grad_x), np.std(grad_x), np.mean(grad_y), np.std(grad_y),
            np.mean(np.abs(grad_x)), np.mean(np.abs(grad_y))
        ]
        
        # 扩展到100维
        texture_features.extend([0] * (100 - len(texture_features)))
        hist_features.extend(texture_features[:100])
        
        # 计算形状特征 (边缘密度等，100维)
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        
        shape_features = [edge_density]
        shape_features.extend([0] * (100 - len(shape_features)))
        hist_features.extend(shape_features[:100])
        
        # 其他特征填充到400维
        while len(hist_features) < target_dim:
            hist_features.append(0)
        
        features.append(hist_features[:target_dim])
    
    # 转换为numpy数组并转置
    features = np.array(features).T  # (400, num_frames)
    
    return features

def create_annotation_for_segments(segments_info, output_path):
    """
    为分段创建TAD格式的标注文件
    """
    annotations = {}
    
    for segment in segments_info:
        video_key = segment['feature_file'].replace('.csv', '')
        
        annotations[video_key] = {
            'duration_second': segment['duration'],
            'duration_frame': segment['feature_frame'],
            'feature_frame': segment['feature_frame'],
            'annotations': [
                # 默认假设整个段落都可能包含动作
                {
                    'segment': [0, segment['duration']],
                    'label': 'unknown'  # 待检测
                }
            ]
        }
    
    # 保存标注文件
    with open(output_path, 'w') as f:
        json.dump(annotations, f, indent=2)
    
    print(f"标注文件已保存到: {output_path}")
    return annotations

def run_tad_inference(config_path, checkpoint_path, feature_dir, annotation_path, output_dir):
    """
    运行TAD推理
    """
    print("开始TAD推理...")
    
    # 加载配置
    cfg = Config.fromfile(config_path)
    
    # 修改配置以使用我们的数据
    cfg.test_dataloader.dataset.ann_file = annotation_path
    cfg.test_dataloader.dataset.data_prefix.video = feature_dir + '/'
    cfg.test_dataloader.batch_size = 1
    
    # 设置工作目录
    cfg.work_dir = output_dir
    cfg.load_from = checkpoint_path
    
    # 创建runner并运行推理
    runner = Runner.from_cfg(cfg)
    runner.test()
    
    print(f"推理完成，结果保存在: {output_dir}")

def parse_inference_results(results_dir, segments_info):
    """
    解析推理结果并合并分段结果
    """
    # 查找结果文件
    results_files = list(Path(results_dir).glob("**/results.json"))
    
    if not results_files:
        print("未找到推理结果文件")
        return None
    
    results_file = results_files[0]
    print(f"解析结果文件: {results_file}")
    
    # 这里需要根据实际的结果格式进行解析
    # BMN模型的结果通常包含检测到的动作段落
    
    # 简化的结果解析示例
    final_results = {
        'segments': segments_info,
        'detections': [],
        'summary': {
            'total_segments': len(segments_info),
            'processing_completed': True
        }
    }
    
    return final_results

def main():
    parser = argparse.ArgumentParser(description='长视频TAD推理')
    parser.add_argument('video_path', help='输入视频路径')
    parser.add_argument('config', help='模型配置文件路径')
    parser.add_argument('checkpoint', help='模型检查点路径')
    parser.add_argument('--output-dir', default='./tad_inference_results', help='输出目录')
    parser.add_argument('--segment-duration', type=float, default=5.0, help='分段时长（秒）')
    parser.add_argument('--overlap', type=float, default=1.0, help='重叠时长（秒）')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    feature_dir = output_dir / 'features'
    feature_dir.mkdir(exist_ok=True)
    
    # 步骤1: 提取特征
    print("=== 步骤1: 提取视频特征 ===")
    segments_info = extract_features_from_video(
        args.video_path, 
        str(feature_dir),
        args.segment_duration,
        args.overlap
    )
    
    # 步骤2: 创建标注文件
    print("=== 步骤2: 创建标注文件 ===")
    annotation_path = output_dir / 'annotations.json'
    create_annotation_for_segments(segments_info, str(annotation_path))
    
    # 步骤3: 运行TAD推理
    print("=== 步骤3: 运行TAD推理 ===")
    inference_output_dir = output_dir / 'inference_results'
    inference_output_dir.mkdir(exist_ok=True)
    
    run_tad_inference(
        args.config,
        args.checkpoint,
        str(feature_dir),
        str(annotation_path),
        str(inference_output_dir)
    )
    
    # 步骤4: 解析结果
    print("=== 步骤4: 解析推理结果 ===")
    final_results = parse_inference_results(str(inference_output_dir), segments_info)
    
    # 保存最终结果
    final_results_path = output_dir / 'final_results.json'
    with open(final_results_path, 'w') as f:
        json.dump(final_results, f, indent=2)
    
    print(f"=== 推理完成 ===")
    print(f"最终结果保存在: {final_results_path}")

if __name__ == '__main__':
    main()
